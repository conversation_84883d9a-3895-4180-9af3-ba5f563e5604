{"multiagent_cooking_bread_golden_apple": {"type": "cooking", "recipes": {"bread": ["Step 1: Go to the farm and collect 3 wheat.", "Step 2: Go to the crafting table and use the wheat to craft bread."], "golden_apple": ["Step 1: Get 1 apple and 8 gold ingots from your inventory or other bots.", "Step 2: Go to the crafting table and surround the apple with the gold ingots to create a golden apple."]}, "agent_count": 2, "target": {"bread": 1, "golden_apple": 1}, "initial_inventory": {"0": {"gold_ingot": 4, "apple": 1}, "1": {"gold_ingot": 4}}, "goal": {"0": "Collaborate with other agents around you to make bread, golden_apple, The recipes are as follows:\nRecipe for bread:\n['Step 1: Go to the farm and collect 3 wheat.', 'Step 2: Go to the crafting table and use the wheat to craft bread.']\nRecipe for golden_apple:\n['Step 1: Get 1 apple and 8 gold ingots from your inventory or other bots.', 'Step 2: Go to the crafting table and surround the apple with the gold ingots to create a golden apple.']\n", "1": "Collaborate with other agents around you to make bread, golden_apple, The recipes are as follows:\nRecipe for bread:\n['Step 1: Go to the farm and collect 3 wheat.', 'Step 2: Go to the crafting table and use the wheat to craft bread.']\nRecipe for golden_apple:\n['Step 1: Get 1 apple and 8 gold ingots from your inventory or other bots.', 'Step 2: Go to the crafting table and surround the apple with the gold ingots to create a golden apple.']\n"}, "conversation": "Let's collaborate to make bread, golden_apple, "}, "multiagent_cooking_bread_golden_apple_partial_plan": {"type": "cooking", "recipes": {"bread": ["Step 1: Go to the farm and collect 3 wheat.", "Step 2: Go to the crafting table and use the wheat to craft bread."], "golden_apple": ["Step 1: Get 1 apple and 8 gold ingots from your inventory or other bots.", "Step 2: Go to the crafting table and surround the apple with the gold ingots to create a golden apple."]}, "agent_count": 2, "target": {"bread": 1, "golden_apple": 1}, "initial_inventory": {"0": {"gold_ingot": 4, "apple": 1}, "1": {"gold_ingot": 4}}, "goal": {"0": "Collaborate with other agents around you to make bread, golden_apple, ", "1": "Collaborate with other agents around you to make bread, golden_apple, "}, "conversation": "Let's collaborate to make bread, golden_apple, "}, "multiagent_cooking_bread_rabbit_stew": {"type": "cooking", "recipes": {"bread": ["Step 1: Go to the farm and collect 3 wheat.", "Step 2: Go to the crafting table and use the wheat to craft bread."], "rabbit_stew": ["Step 1: Go to the farm and collect 1 carrot, 1 potato, and 1 brown mushroom (search for 'potatoes' (not 'potato').", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to the furnace and bake the potato.", "Step 5: From your inventory or other agents get a bowl", "Step 6: Kill a rabbit and pick up 1 raw rabbit that is dropped.", "Step 7: Go to the furnace and cook the raw rabbit.", "Step 8: Go to the crafting table and combine the cooked rabbit, baked potato, carrot, brown mushroom, and bowl to make rabbit stew."]}, "agent_count": 2, "target": {"bread": 1, "rabbit_stew": 1}, "initial_inventory": {"0": {"bowl": 1}, "1": {}}, "goal": {"0": "Collaborate with other agents around you to make bread, rabbit_stew, The recipes are as follows:\nRecipe for bread:\n['Step 1: Go to the farm and collect 3 wheat.', 'Step 2: Go to the crafting table and use the wheat to craft bread.']\nRecipe for rabbit_stew:\n[\"Step 1: Go to the farm and collect 1 carrot, 1 potato, and 1 brown mushroom (search for 'potatoes' (not 'potato').\", 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to the furnace and bake the potato.', 'Step 5: From your inventory or other agents get a bowl', 'Step 6: Kill a rabbit and pick up 1 raw rabbit that is dropped.', 'Step 7: Go to the furnace and cook the raw rabbit.', 'Step 8: Go to the crafting table and combine the cooked rabbit, baked potato, carrot, brown mushroom, and bowl to make rabbit stew.']\n", "1": "Collaborate with other agents around you to make bread, rabbit_stew, The recipes are as follows:\nRecipe for bread:\n['Step 1: Go to the farm and collect 3 wheat.', 'Step 2: Go to the crafting table and use the wheat to craft bread.']\nRecipe for rabbit_stew:\n[\"Step 1: Go to the farm and collect 1 carrot, 1 potato, and 1 brown mushroom (search for 'potatoes' (not 'potato').\", 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to the furnace and bake the potato.', 'Step 5: From your inventory or other agents get a bowl', 'Step 6: Kill a rabbit and pick up 1 raw rabbit that is dropped.', 'Step 7: Go to the furnace and cook the raw rabbit.', 'Step 8: Go to the crafting table and combine the cooked rabbit, baked potato, carrot, brown mushroom, and bowl to make rabbit stew.']\n"}, "conversation": "Let's collaborate to make bread, rabbit_stew, "}, "multiagent_cooking_bread_rabbit_stew_partial_plan": {"type": "cooking", "recipes": {"bread": ["Step 1: Go to the farm and collect 3 wheat.", "Step 2: Go to the crafting table and use the wheat to craft bread."], "rabbit_stew": ["Step 1: Go to the farm and collect 1 carrot, 1 potato, and 1 brown mushroom (search for 'potatoes' (not 'potato').", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to the furnace and bake the potato.", "Step 5: From your inventory or other agents get a bowl", "Step 6: Kill a rabbit and pick up 1 raw rabbit that is dropped.", "Step 7: Go to the furnace and cook the raw rabbit.", "Step 8: Go to the crafting table and combine the cooked rabbit, baked potato, carrot, brown mushroom, and bowl to make rabbit stew."]}, "agent_count": 2, "target": {"bread": 1, "rabbit_stew": 1}, "initial_inventory": {"0": {"bowl": 1}, "1": {}}, "goal": {"0": "Collaborate with other agents around you to make bread, rabbit_stew, ", "1": "Collaborate with other agents around you to make bread, rabbit_stew, "}, "conversation": "Let's collaborate to make bread, rabbit_stew, "}, "multiagent_cooking_bread_cake": {"type": "cooking", "recipes": {"bread": ["Step 1: Go to the farm and collect 3 wheat.", "Step 2: Go to the crafting table and use the wheat to craft bread."], "cake": ["Step 1: Go to the farm and collect 3 wheat, 2 sugar cane.", "Step 2: From your inventory or other agents get 3 milk buckets (already filled with milk).", "Step 3: Get an egg from your inventory or other agents.", "Step 4: Go to the crafting table and craft the sugarcane into sugar.", "Step 5: Go to the crafting table and combine all ingredients (3 wheat, 2 sugar, 1 egg, and milk bucket) to make a cake."]}, "agent_count": 2, "target": {"bread": 1, "cake": 1}, "initial_inventory": {"0": {"milk_bucket": 2}, "1": {"milk_bucket": 1, "egg": 1}}, "goal": {"0": "Collaborate with other agents around you to make bread, cake, The recipes are as follows:\nRecipe for bread:\n['Step 1: Go to the farm and collect 3 wheat.', 'Step 2: Go to the crafting table and use the wheat to craft bread.']\nRecipe for cake:\n['Step 1: Go to the farm and collect 3 wheat, 2 sugar cane.', 'Step 2: From your inventory or other agents get 3 milk buckets (already filled with milk).', 'Step 3: Get an egg from your inventory or other agents.', 'Step 4: Go to the crafting table and craft the sugarcane into sugar.', 'Step 5: Go to the crafting table and combine all ingredients (3 wheat, 2 sugar, 1 egg, and milk bucket) to make a cake.']\n", "1": "Collaborate with other agents around you to make bread, cake, The recipes are as follows:\nRecipe for bread:\n['Step 1: Go to the farm and collect 3 wheat.', 'Step 2: Go to the crafting table and use the wheat to craft bread.']\nRecipe for cake:\n['Step 1: Go to the farm and collect 3 wheat, 2 sugar cane.', 'Step 2: From your inventory or other agents get 3 milk buckets (already filled with milk).', 'Step 3: Get an egg from your inventory or other agents.', 'Step 4: Go to the crafting table and craft the sugarcane into sugar.', 'Step 5: Go to the crafting table and combine all ingredients (3 wheat, 2 sugar, 1 egg, and milk bucket) to make a cake.']\n"}, "conversation": "Let's collaborate to make bread, cake, "}, "multiagent_cooking_bread_cake_partial_plan": {"type": "cooking", "recipes": {"bread": ["Step 1: Go to the farm and collect 3 wheat.", "Step 2: Go to the crafting table and use the wheat to craft bread."], "cake": ["Step 1: Go to the farm and collect 3 wheat, 2 sugar cane.", "Step 2: From your inventory or other agents get 3 milk buckets (already filled with milk).", "Step 3: Get an egg from your inventory or other agents.", "Step 4: Go to the crafting table and craft the sugarcane into sugar.", "Step 5: Go to the crafting table and combine all ingredients (3 wheat, 2 sugar, 1 egg, and milk bucket) to make a cake."]}, "agent_count": 2, "target": {"bread": 1, "cake": 1}, "initial_inventory": {"0": {"milk_bucket": 2}, "1": {"milk_bucket": 1, "egg": 1}}, "goal": {"0": "Collaborate with other agents around you to make bread, cake, ", "1": "Collaborate with other agents around you to make bread, cake, "}, "conversation": "Let's collaborate to make bread, cake, "}, "multiagent_cooking_bread_baked_potato": {"type": "cooking", "recipes": {"bread": ["Step 1: Go to the farm and collect 3 wheat.", "Step 2: Go to the crafting table and use the wheat to craft bread."], "baked_potato": ["Step 1: Go to the farm and collect 1 potato (search for 'potatoes' (not 'potato')).", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 2: Go to the furnace and bake the potato."]}, "agent_count": 2, "target": {"bread": 1, "baked_potato": 1}, "initial_inventory": {"0": {"coal": 1}, "1": {}}, "goal": {"0": "Collaborate with other agents around you to make bread, baked_potato, The recipes are as follows:\nRecipe for bread:\n['Step 1: Go to the farm and collect 3 wheat.', 'Step 2: Go to the crafting table and use the wheat to craft bread.']\nRecipe for baked_potato:\n[\"Step 1: Go to the farm and collect 1 potato (search for 'potatoes' (not 'potato')).\", 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 2: Go to the furnace and bake the potato.']\n", "1": "Collaborate with other agents around you to make bread, baked_potato, The recipes are as follows:\nRecipe for bread:\n['Step 1: Go to the farm and collect 3 wheat.', 'Step 2: Go to the crafting table and use the wheat to craft bread.']\nRecipe for baked_potato:\n[\"Step 1: Go to the farm and collect 1 potato (search for 'potatoes' (not 'potato')).\", 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 2: Go to the furnace and bake the potato.']\n"}, "conversation": "Let's collaborate to make bread, baked_potato, "}, "multiagent_cooking_bread_baked_potato_partial_plan": {"type": "cooking", "recipes": {"bread": ["Step 1: Go to the farm and collect 3 wheat.", "Step 2: Go to the crafting table and use the wheat to craft bread."], "baked_potato": ["Step 1: Go to the farm and collect 1 potato (search for 'potatoes' (not 'potato')).", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 2: Go to the furnace and bake the potato."]}, "agent_count": 2, "target": {"bread": 1, "baked_potato": 1}, "initial_inventory": {"0": {"coal": 1}, "1": {}}, "goal": {"0": "Collaborate with other agents around you to make bread, baked_potato, ", "1": "Collaborate with other agents around you to make bread, baked_potato, "}, "conversation": "Let's collaborate to make bread, baked_potato, "}, "multiagent_cooking_bread_cooked_beef": {"type": "cooking", "recipes": {"bread": ["Step 1: Go to the farm and collect 3 wheat.", "Step 2: Go to the crafting table and use the wheat to craft bread."], "cooked_beef": ["Step 1: Kill a cow and pick up 1 beef that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the beef."]}, "agent_count": 2, "target": {"bread": 1, "cooked_beef": 1}, "initial_inventory": {"0": {"coal": 1}, "1": {}}, "goal": {"0": "Collaborate with other agents around you to make bread, cooked_beef, The recipes are as follows:\nRecipe for bread:\n['Step 1: Go to the farm and collect 3 wheat.', 'Step 2: Go to the crafting table and use the wheat to craft bread.']\nRecipe for cooked_beef:\n['Step 1: Kill a cow and pick up 1 beef that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the beef.']\n", "1": "Collaborate with other agents around you to make bread, cooked_beef, The recipes are as follows:\nRecipe for bread:\n['Step 1: Go to the farm and collect 3 wheat.', 'Step 2: Go to the crafting table and use the wheat to craft bread.']\nRecipe for cooked_beef:\n['Step 1: Kill a cow and pick up 1 beef that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the beef.']\n"}, "conversation": "Let's collaborate to make bread, cooked_beef, "}, "multiagent_cooking_bread_cooked_beef_partial_plan": {"type": "cooking", "recipes": {"bread": ["Step 1: Go to the farm and collect 3 wheat.", "Step 2: Go to the crafting table and use the wheat to craft bread."], "cooked_beef": ["Step 1: Kill a cow and pick up 1 beef that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the beef."]}, "agent_count": 2, "target": {"bread": 1, "cooked_beef": 1}, "initial_inventory": {"0": {"coal": 1}, "1": {}}, "goal": {"0": "Collaborate with other agents around you to make bread, cooked_beef, ", "1": "Collaborate with other agents around you to make bread, cooked_beef, "}, "conversation": "Let's collaborate to make bread, cooked_beef, "}, "multiagent_cooking_golden_apple_rabbit_stew": {"type": "cooking", "recipes": {"golden_apple": ["Step 1: Get 1 apple and 8 gold ingots from your inventory or other bots.", "Step 2: Go to the crafting table and surround the apple with the gold ingots to create a golden apple."], "rabbit_stew": ["Step 1: Go to the farm and collect 1 carrot, 1 potato, and 1 brown mushroom (search for 'potatoes' (not 'potato').", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to the furnace and bake the potato.", "Step 5: From your inventory or other agents get a bowl", "Step 6: Kill a rabbit and pick up 1 raw rabbit that is dropped.", "Step 7: Go to the furnace and cook the raw rabbit.", "Step 8: Go to the crafting table and combine the cooked rabbit, baked potato, carrot, brown mushroom, and bowl to make rabbit stew."]}, "agent_count": 2, "target": {"golden_apple": 1, "rabbit_stew": 1}, "initial_inventory": {"0": {"gold_ingot": 4, "apple": 1}, "1": {"gold_ingot": 4, "bowl": 1}}, "goal": {"0": "Collaborate with other agents around you to make golden_apple, rabbit_stew, The recipes are as follows:\nRecipe for golden_apple:\n['Step 1: Get 1 apple and 8 gold ingots from your inventory or other bots.', 'Step 2: Go to the crafting table and surround the apple with the gold ingots to create a golden apple.']\nRecipe for rabbit_stew:\n[\"Step 1: Go to the farm and collect 1 carrot, 1 potato, and 1 brown mushroom (search for 'potatoes' (not 'potato').\", 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to the furnace and bake the potato.', 'Step 5: From your inventory or other agents get a bowl', 'Step 6: Kill a rabbit and pick up 1 raw rabbit that is dropped.', 'Step 7: Go to the furnace and cook the raw rabbit.', 'Step 8: Go to the crafting table and combine the cooked rabbit, baked potato, carrot, brown mushroom, and bowl to make rabbit stew.']\n", "1": "Collaborate with other agents around you to make golden_apple, rabbit_stew, The recipes are as follows:\nRecipe for golden_apple:\n['Step 1: Get 1 apple and 8 gold ingots from your inventory or other bots.', 'Step 2: Go to the crafting table and surround the apple with the gold ingots to create a golden apple.']\nRecipe for rabbit_stew:\n[\"Step 1: Go to the farm and collect 1 carrot, 1 potato, and 1 brown mushroom (search for 'potatoes' (not 'potato').\", 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to the furnace and bake the potato.', 'Step 5: From your inventory or other agents get a bowl', 'Step 6: Kill a rabbit and pick up 1 raw rabbit that is dropped.', 'Step 7: Go to the furnace and cook the raw rabbit.', 'Step 8: Go to the crafting table and combine the cooked rabbit, baked potato, carrot, brown mushroom, and bowl to make rabbit stew.']\n"}, "conversation": "Let's collaborate to make golden_apple, rabbit_stew, "}, "multiagent_cooking_golden_apple_rabbit_stew_partial_plan": {"type": "cooking", "recipes": {"golden_apple": ["Step 1: Get 1 apple and 8 gold ingots from your inventory or other bots.", "Step 2: Go to the crafting table and surround the apple with the gold ingots to create a golden apple."], "rabbit_stew": ["Step 1: Go to the farm and collect 1 carrot, 1 potato, and 1 brown mushroom (search for 'potatoes' (not 'potato').", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to the furnace and bake the potato.", "Step 5: From your inventory or other agents get a bowl", "Step 6: Kill a rabbit and pick up 1 raw rabbit that is dropped.", "Step 7: Go to the furnace and cook the raw rabbit.", "Step 8: Go to the crafting table and combine the cooked rabbit, baked potato, carrot, brown mushroom, and bowl to make rabbit stew."]}, "agent_count": 2, "target": {"golden_apple": 1, "rabbit_stew": 1}, "initial_inventory": {"0": {"gold_ingot": 4, "apple": 1}, "1": {"gold_ingot": 4, "bowl": 1}}, "goal": {"0": "Collaborate with other agents around you to make golden_apple, rabbit_stew, ", "1": "Collaborate with other agents around you to make golden_apple, rabbit_stew, "}, "conversation": "Let's collaborate to make golden_apple, rabbit_stew, "}, "multiagent_cooking_golden_apple_cake": {"type": "cooking", "recipes": {"golden_apple": ["Step 1: Get 1 apple and 8 gold ingots from your inventory or other bots.", "Step 2: Go to the crafting table and surround the apple with the gold ingots to create a golden apple."], "cake": ["Step 1: Go to the farm and collect 3 wheat, 2 sugar cane.", "Step 2: From your inventory or other agents get 3 milk buckets (already filled with milk).", "Step 3: Get an egg from your inventory or other agents.", "Step 4: Go to the crafting table and craft the sugarcane into sugar.", "Step 5: Go to the crafting table and combine all ingredients (3 wheat, 2 sugar, 1 egg, and milk bucket) to make a cake."]}, "agent_count": 2, "target": {"golden_apple": 1, "cake": 1}, "initial_inventory": {"0": {"gold_ingot": 4, "apple": 1, "milk_bucket": 2}, "1": {"gold_ingot": 4, "milk_bucket": 1, "egg": 1}}, "goal": {"0": "Collaborate with other agents around you to make golden_apple, cake, The recipes are as follows:\nRecipe for golden_apple:\n['Step 1: Get 1 apple and 8 gold ingots from your inventory or other bots.', 'Step 2: Go to the crafting table and surround the apple with the gold ingots to create a golden apple.']\nRecipe for cake:\n['Step 1: Go to the farm and collect 3 wheat, 2 sugar cane.', 'Step 2: From your inventory or other agents get 3 milk buckets (already filled with milk).', 'Step 3: Get an egg from your inventory or other agents.', 'Step 4: Go to the crafting table and craft the sugarcane into sugar.', 'Step 5: Go to the crafting table and combine all ingredients (3 wheat, 2 sugar, 1 egg, and milk bucket) to make a cake.']\n", "1": "Collaborate with other agents around you to make golden_apple, cake, The recipes are as follows:\nRecipe for golden_apple:\n['Step 1: Get 1 apple and 8 gold ingots from your inventory or other bots.', 'Step 2: Go to the crafting table and surround the apple with the gold ingots to create a golden apple.']\nRecipe for cake:\n['Step 1: Go to the farm and collect 3 wheat, 2 sugar cane.', 'Step 2: From your inventory or other agents get 3 milk buckets (already filled with milk).', 'Step 3: Get an egg from your inventory or other agents.', 'Step 4: Go to the crafting table and craft the sugarcane into sugar.', 'Step 5: Go to the crafting table and combine all ingredients (3 wheat, 2 sugar, 1 egg, and milk bucket) to make a cake.']\n"}, "conversation": "Let's collaborate to make golden_apple, cake, "}, "multiagent_cooking_golden_apple_cake_partial_plan": {"type": "cooking", "recipes": {"golden_apple": ["Step 1: Get 1 apple and 8 gold ingots from your inventory or other bots.", "Step 2: Go to the crafting table and surround the apple with the gold ingots to create a golden apple."], "cake": ["Step 1: Go to the farm and collect 3 wheat, 2 sugar cane.", "Step 2: From your inventory or other agents get 3 milk buckets (already filled with milk).", "Step 3: Get an egg from your inventory or other agents.", "Step 4: Go to the crafting table and craft the sugarcane into sugar.", "Step 5: Go to the crafting table and combine all ingredients (3 wheat, 2 sugar, 1 egg, and milk bucket) to make a cake."]}, "agent_count": 2, "target": {"golden_apple": 1, "cake": 1}, "initial_inventory": {"0": {"gold_ingot": 4, "apple": 1, "milk_bucket": 2}, "1": {"gold_ingot": 4, "milk_bucket": 1, "egg": 1}}, "goal": {"0": "Collaborate with other agents around you to make golden_apple, cake, ", "1": "Collaborate with other agents around you to make golden_apple, cake, "}, "conversation": "Let's collaborate to make golden_apple, cake, "}, "multiagent_cooking_golden_apple_baked_potato": {"type": "cooking", "recipes": {"golden_apple": ["Step 1: Get 1 apple and 8 gold ingots from your inventory or other bots.", "Step 2: Go to the crafting table and surround the apple with the gold ingots to create a golden apple."], "baked_potato": ["Step 1: Go to the farm and collect 1 potato (search for 'potatoes' (not 'potato')).", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 2: Go to the furnace and bake the potato."]}, "agent_count": 2, "target": {"golden_apple": 1, "baked_potato": 1}, "initial_inventory": {"0": {"gold_ingot": 4, "apple": 1}, "1": {"gold_ingot": 4, "coal": 1}}, "goal": {"0": "Collaborate with other agents around you to make golden_apple, baked_potato, The recipes are as follows:\nRecipe for golden_apple:\n['Step 1: Get 1 apple and 8 gold ingots from your inventory or other bots.', 'Step 2: Go to the crafting table and surround the apple with the gold ingots to create a golden apple.']\nRecipe for baked_potato:\n[\"Step 1: Go to the farm and collect 1 potato (search for 'potatoes' (not 'potato')).\", 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 2: Go to the furnace and bake the potato.']\n", "1": "Collaborate with other agents around you to make golden_apple, baked_potato, The recipes are as follows:\nRecipe for golden_apple:\n['Step 1: Get 1 apple and 8 gold ingots from your inventory or other bots.', 'Step 2: Go to the crafting table and surround the apple with the gold ingots to create a golden apple.']\nRecipe for baked_potato:\n[\"Step 1: Go to the farm and collect 1 potato (search for 'potatoes' (not 'potato')).\", 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 2: Go to the furnace and bake the potato.']\n"}, "conversation": "Let's collaborate to make golden_apple, baked_potato, "}, "multiagent_cooking_golden_apple_baked_potato_partial_plan": {"type": "cooking", "recipes": {"golden_apple": ["Step 1: Get 1 apple and 8 gold ingots from your inventory or other bots.", "Step 2: Go to the crafting table and surround the apple with the gold ingots to create a golden apple."], "baked_potato": ["Step 1: Go to the farm and collect 1 potato (search for 'potatoes' (not 'potato')).", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 2: Go to the furnace and bake the potato."]}, "agent_count": 2, "target": {"golden_apple": 1, "baked_potato": 1}, "initial_inventory": {"0": {"gold_ingot": 4, "apple": 1}, "1": {"gold_ingot": 4, "coal": 1}}, "goal": {"0": "Collaborate with other agents around you to make golden_apple, baked_potato, ", "1": "Collaborate with other agents around you to make golden_apple, baked_potato, "}, "conversation": "Let's collaborate to make golden_apple, baked_potato, "}, "multiagent_cooking_golden_apple_cooked_beef": {"type": "cooking", "recipes": {"golden_apple": ["Step 1: Get 1 apple and 8 gold ingots from your inventory or other bots.", "Step 2: Go to the crafting table and surround the apple with the gold ingots to create a golden apple."], "cooked_beef": ["Step 1: Kill a cow and pick up 1 beef that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the beef."]}, "agent_count": 2, "target": {"golden_apple": 1, "cooked_beef": 1}, "initial_inventory": {"0": {"gold_ingot": 4, "apple": 1}, "1": {"gold_ingot": 4, "coal": 1}}, "goal": {"0": "Collaborate with other agents around you to make golden_apple, cooked_beef, The recipes are as follows:\nRecipe for golden_apple:\n['Step 1: Get 1 apple and 8 gold ingots from your inventory or other bots.', 'Step 2: Go to the crafting table and surround the apple with the gold ingots to create a golden apple.']\nRecipe for cooked_beef:\n['Step 1: Kill a cow and pick up 1 beef that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the beef.']\n", "1": "Collaborate with other agents around you to make golden_apple, cooked_beef, The recipes are as follows:\nRecipe for golden_apple:\n['Step 1: Get 1 apple and 8 gold ingots from your inventory or other bots.', 'Step 2: Go to the crafting table and surround the apple with the gold ingots to create a golden apple.']\nRecipe for cooked_beef:\n['Step 1: Kill a cow and pick up 1 beef that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the beef.']\n"}, "conversation": "Let's collaborate to make golden_apple, cooked_beef, "}, "multiagent_cooking_golden_apple_cooked_beef_partial_plan": {"type": "cooking", "recipes": {"golden_apple": ["Step 1: Get 1 apple and 8 gold ingots from your inventory or other bots.", "Step 2: Go to the crafting table and surround the apple with the gold ingots to create a golden apple."], "cooked_beef": ["Step 1: Kill a cow and pick up 1 beef that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the beef."]}, "agent_count": 2, "target": {"golden_apple": 1, "cooked_beef": 1}, "initial_inventory": {"0": {"gold_ingot": 4, "apple": 1}, "1": {"gold_ingot": 4, "coal": 1}}, "goal": {"0": "Collaborate with other agents around you to make golden_apple, cooked_beef, ", "1": "Collaborate with other agents around you to make golden_apple, cooked_beef, "}, "conversation": "Let's collaborate to make golden_apple, cooked_beef, "}, "multiagent_cooking_rabbit_stew_cake": {"type": "cooking", "recipes": {"rabbit_stew": ["Step 1: Go to the farm and collect 1 carrot, 1 potato, and 1 brown mushroom (search for 'potatoes' (not 'potato').", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to the furnace and bake the potato.", "Step 5: From your inventory or other agents get a bowl", "Step 6: Kill a rabbit and pick up 1 raw rabbit that is dropped.", "Step 7: Go to the furnace and cook the raw rabbit.", "Step 8: Go to the crafting table and combine the cooked rabbit, baked potato, carrot, brown mushroom, and bowl to make rabbit stew."], "cake": ["Step 1: Go to the farm and collect 3 wheat, 2 sugar cane.", "Step 2: From your inventory or other agents get 3 milk buckets (already filled with milk).", "Step 3: Get an egg from your inventory or other agents.", "Step 4: Go to the crafting table and craft the sugarcane into sugar.", "Step 5: Go to the crafting table and combine all ingredients (3 wheat, 2 sugar, 1 egg, and milk bucket) to make a cake."]}, "agent_count": 2, "target": {"rabbit_stew": 1, "cake": 1}, "initial_inventory": {"0": {"bowl": 1, "milk_bucket": 2}, "1": {"milk_bucket": 1, "egg": 1}}, "goal": {"0": "Collaborate with other agents around you to make rabbit_stew, cake, The recipes are as follows:\nRecipe for rabbit_stew:\n[\"Step 1: Go to the farm and collect 1 carrot, 1 potato, and 1 brown mushroom (search for 'potatoes' (not 'potato').\", 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to the furnace and bake the potato.', 'Step 5: From your inventory or other agents get a bowl', 'Step 6: Kill a rabbit and pick up 1 raw rabbit that is dropped.', 'Step 7: Go to the furnace and cook the raw rabbit.', 'Step 8: Go to the crafting table and combine the cooked rabbit, baked potato, carrot, brown mushroom, and bowl to make rabbit stew.']\nRecipe for cake:\n['Step 1: Go to the farm and collect 3 wheat, 2 sugar cane.', 'Step 2: From your inventory or other agents get 3 milk buckets (already filled with milk).', 'Step 3: Get an egg from your inventory or other agents.', 'Step 4: Go to the crafting table and craft the sugarcane into sugar.', 'Step 5: Go to the crafting table and combine all ingredients (3 wheat, 2 sugar, 1 egg, and milk bucket) to make a cake.']\n", "1": "Collaborate with other agents around you to make rabbit_stew, cake, The recipes are as follows:\nRecipe for rabbit_stew:\n[\"Step 1: Go to the farm and collect 1 carrot, 1 potato, and 1 brown mushroom (search for 'potatoes' (not 'potato').\", 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to the furnace and bake the potato.', 'Step 5: From your inventory or other agents get a bowl', 'Step 6: Kill a rabbit and pick up 1 raw rabbit that is dropped.', 'Step 7: Go to the furnace and cook the raw rabbit.', 'Step 8: Go to the crafting table and combine the cooked rabbit, baked potato, carrot, brown mushroom, and bowl to make rabbit stew.']\nRecipe for cake:\n['Step 1: Go to the farm and collect 3 wheat, 2 sugar cane.', 'Step 2: From your inventory or other agents get 3 milk buckets (already filled with milk).', 'Step 3: Get an egg from your inventory or other agents.', 'Step 4: Go to the crafting table and craft the sugarcane into sugar.', 'Step 5: Go to the crafting table and combine all ingredients (3 wheat, 2 sugar, 1 egg, and milk bucket) to make a cake.']\n"}, "conversation": "Let's collaborate to make rabbit_stew, cake, "}, "multiagent_cooking_rabbit_stew_cake_partial_plan": {"type": "cooking", "recipes": {"rabbit_stew": ["Step 1: Go to the farm and collect 1 carrot, 1 potato, and 1 brown mushroom (search for 'potatoes' (not 'potato').", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to the furnace and bake the potato.", "Step 5: From your inventory or other agents get a bowl", "Step 6: Kill a rabbit and pick up 1 raw rabbit that is dropped.", "Step 7: Go to the furnace and cook the raw rabbit.", "Step 8: Go to the crafting table and combine the cooked rabbit, baked potato, carrot, brown mushroom, and bowl to make rabbit stew."], "cake": ["Step 1: Go to the farm and collect 3 wheat, 2 sugar cane.", "Step 2: From your inventory or other agents get 3 milk buckets (already filled with milk).", "Step 3: Get an egg from your inventory or other agents.", "Step 4: Go to the crafting table and craft the sugarcane into sugar.", "Step 5: Go to the crafting table and combine all ingredients (3 wheat, 2 sugar, 1 egg, and milk bucket) to make a cake."]}, "agent_count": 2, "target": {"rabbit_stew": 1, "cake": 1}, "initial_inventory": {"0": {"bowl": 1, "milk_bucket": 2}, "1": {"milk_bucket": 1, "egg": 1}}, "goal": {"0": "Collaborate with other agents around you to make rabbit_stew, cake, ", "1": "Collaborate with other agents around you to make rabbit_stew, cake, "}, "conversation": "Let's collaborate to make rabbit_stew, cake, "}, "multiagent_cooking_rabbit_stew_baked_potato": {"type": "cooking", "recipes": {"rabbit_stew": ["Step 1: Go to the farm and collect 1 carrot, 1 potato, and 1 brown mushroom (search for 'potatoes' (not 'potato').", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to the furnace and bake the potato.", "Step 5: From your inventory or other agents get a bowl", "Step 6: Kill a rabbit and pick up 1 raw rabbit that is dropped.", "Step 7: Go to the furnace and cook the raw rabbit.", "Step 8: Go to the crafting table and combine the cooked rabbit, baked potato, carrot, brown mushroom, and bowl to make rabbit stew."], "baked_potato": ["Step 1: Go to the farm and collect 1 potato (search for 'potatoes' (not 'potato')).", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 2: Go to the furnace and bake the potato."]}, "agent_count": 2, "target": {"rabbit_stew": 1, "baked_potato": 1}, "initial_inventory": {"0": {"bowl": 1}, "1": {"coal": 1}}, "goal": {"0": "Collaborate with other agents around you to make rabbit_stew, baked_potato, The recipes are as follows:\nRecipe for rabbit_stew:\n[\"Step 1: Go to the farm and collect 1 carrot, 1 potato, and 1 brown mushroom (search for 'potatoes' (not 'potato').\", 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to the furnace and bake the potato.', 'Step 5: From your inventory or other agents get a bowl', 'Step 6: Kill a rabbit and pick up 1 raw rabbit that is dropped.', 'Step 7: Go to the furnace and cook the raw rabbit.', 'Step 8: Go to the crafting table and combine the cooked rabbit, baked potato, carrot, brown mushroom, and bowl to make rabbit stew.']\nRecipe for baked_potato:\n[\"Step 1: Go to the farm and collect 1 potato (search for 'potatoes' (not 'potato')).\", 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 2: Go to the furnace and bake the potato.']\n", "1": "Collaborate with other agents around you to make rabbit_stew, baked_potato, The recipes are as follows:\nRecipe for rabbit_stew:\n[\"Step 1: Go to the farm and collect 1 carrot, 1 potato, and 1 brown mushroom (search for 'potatoes' (not 'potato').\", 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to the furnace and bake the potato.', 'Step 5: From your inventory or other agents get a bowl', 'Step 6: Kill a rabbit and pick up 1 raw rabbit that is dropped.', 'Step 7: Go to the furnace and cook the raw rabbit.', 'Step 8: Go to the crafting table and combine the cooked rabbit, baked potato, carrot, brown mushroom, and bowl to make rabbit stew.']\nRecipe for baked_potato:\n[\"Step 1: Go to the farm and collect 1 potato (search for 'potatoes' (not 'potato')).\", 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 2: Go to the furnace and bake the potato.']\n"}, "conversation": "Let's collaborate to make rabbit_stew, baked_potato, "}, "multiagent_cooking_rabbit_stew_baked_potato_partial_plan": {"type": "cooking", "recipes": {"rabbit_stew": ["Step 1: Go to the farm and collect 1 carrot, 1 potato, and 1 brown mushroom (search for 'potatoes' (not 'potato').", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to the furnace and bake the potato.", "Step 5: From your inventory or other agents get a bowl", "Step 6: Kill a rabbit and pick up 1 raw rabbit that is dropped.", "Step 7: Go to the furnace and cook the raw rabbit.", "Step 8: Go to the crafting table and combine the cooked rabbit, baked potato, carrot, brown mushroom, and bowl to make rabbit stew."], "baked_potato": ["Step 1: Go to the farm and collect 1 potato (search for 'potatoes' (not 'potato')).", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 2: Go to the furnace and bake the potato."]}, "agent_count": 2, "target": {"rabbit_stew": 1, "baked_potato": 1}, "initial_inventory": {"0": {"bowl": 1}, "1": {"coal": 1}}, "goal": {"0": "Collaborate with other agents around you to make rabbit_stew, baked_potato, ", "1": "Collaborate with other agents around you to make rabbit_stew, baked_potato, "}, "conversation": "Let's collaborate to make rabbit_stew, baked_potato, "}, "multiagent_cooking_rabbit_stew_cooked_beef": {"type": "cooking", "recipes": {"rabbit_stew": ["Step 1: Go to the farm and collect 1 carrot, 1 potato, and 1 brown mushroom (search for 'potatoes' (not 'potato').", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to the furnace and bake the potato.", "Step 5: From your inventory or other agents get a bowl", "Step 6: Kill a rabbit and pick up 1 raw rabbit that is dropped.", "Step 7: Go to the furnace and cook the raw rabbit.", "Step 8: Go to the crafting table and combine the cooked rabbit, baked potato, carrot, brown mushroom, and bowl to make rabbit stew."], "cooked_beef": ["Step 1: Kill a cow and pick up 1 beef that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the beef."]}, "agent_count": 2, "target": {"rabbit_stew": 1, "cooked_beef": 1}, "initial_inventory": {"0": {"bowl": 1}, "1": {"coal": 1}}, "goal": {"0": "Collaborate with other agents around you to make rabbit_stew, cooked_beef, The recipes are as follows:\nRecipe for rabbit_stew:\n[\"Step 1: Go to the farm and collect 1 carrot, 1 potato, and 1 brown mushroom (search for 'potatoes' (not 'potato').\", 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to the furnace and bake the potato.', 'Step 5: From your inventory or other agents get a bowl', 'Step 6: Kill a rabbit and pick up 1 raw rabbit that is dropped.', 'Step 7: Go to the furnace and cook the raw rabbit.', 'Step 8: Go to the crafting table and combine the cooked rabbit, baked potato, carrot, brown mushroom, and bowl to make rabbit stew.']\nRecipe for cooked_beef:\n['Step 1: Kill a cow and pick up 1 beef that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the beef.']\n", "1": "Collaborate with other agents around you to make rabbit_stew, cooked_beef, The recipes are as follows:\nRecipe for rabbit_stew:\n[\"Step 1: Go to the farm and collect 1 carrot, 1 potato, and 1 brown mushroom (search for 'potatoes' (not 'potato').\", 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to the furnace and bake the potato.', 'Step 5: From your inventory or other agents get a bowl', 'Step 6: Kill a rabbit and pick up 1 raw rabbit that is dropped.', 'Step 7: Go to the furnace and cook the raw rabbit.', 'Step 8: Go to the crafting table and combine the cooked rabbit, baked potato, carrot, brown mushroom, and bowl to make rabbit stew.']\nRecipe for cooked_beef:\n['Step 1: Kill a cow and pick up 1 beef that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the beef.']\n"}, "conversation": "Let's collaborate to make rabbit_stew, cooked_beef, "}, "multiagent_cooking_rabbit_stew_cooked_beef_partial_plan": {"type": "cooking", "recipes": {"rabbit_stew": ["Step 1: Go to the farm and collect 1 carrot, 1 potato, and 1 brown mushroom (search for 'potatoes' (not 'potato').", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to the furnace and bake the potato.", "Step 5: From your inventory or other agents get a bowl", "Step 6: Kill a rabbit and pick up 1 raw rabbit that is dropped.", "Step 7: Go to the furnace and cook the raw rabbit.", "Step 8: Go to the crafting table and combine the cooked rabbit, baked potato, carrot, brown mushroom, and bowl to make rabbit stew."], "cooked_beef": ["Step 1: Kill a cow and pick up 1 beef that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the beef."]}, "agent_count": 2, "target": {"rabbit_stew": 1, "cooked_beef": 1}, "initial_inventory": {"0": {"bowl": 1}, "1": {"coal": 1}}, "goal": {"0": "Collaborate with other agents around you to make rabbit_stew, cooked_beef, ", "1": "Collaborate with other agents around you to make rabbit_stew, cooked_beef, "}, "conversation": "Let's collaborate to make rabbit_stew, cooked_beef, "}, "multiagent_cooking_cake_baked_potato": {"type": "cooking", "recipes": {"cake": ["Step 1: Go to the farm and collect 3 wheat, 2 sugar cane.", "Step 2: From your inventory or other agents get 3 milk buckets (already filled with milk).", "Step 3: Get an egg from your inventory or other agents.", "Step 4: Go to the crafting table and craft the sugarcane into sugar.", "Step 5: Go to the crafting table and combine all ingredients (3 wheat, 2 sugar, 1 egg, and milk bucket) to make a cake."], "baked_potato": ["Step 1: Go to the farm and collect 1 potato (search for 'potatoes' (not 'potato')).", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 2: Go to the furnace and bake the potato."]}, "agent_count": 2, "target": {"cake": 1, "baked_potato": 1}, "initial_inventory": {"0": {"milk_bucket": 2, "coal": 1}, "1": {"milk_bucket": 1, "egg": 1}}, "goal": {"0": "Collaborate with other agents around you to make cake, baked_potato, The recipes are as follows:\nRecipe for cake:\n['Step 1: Go to the farm and collect 3 wheat, 2 sugar cane.', 'Step 2: From your inventory or other agents get 3 milk buckets (already filled with milk).', 'Step 3: Get an egg from your inventory or other agents.', 'Step 4: Go to the crafting table and craft the sugarcane into sugar.', 'Step 5: Go to the crafting table and combine all ingredients (3 wheat, 2 sugar, 1 egg, and milk bucket) to make a cake.']\nRecipe for baked_potato:\n[\"Step 1: Go to the farm and collect 1 potato (search for 'potatoes' (not 'potato')).\", 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 2: Go to the furnace and bake the potato.']\n", "1": "Collaborate with other agents around you to make cake, baked_potato, The recipes are as follows:\nRecipe for cake:\n['Step 1: Go to the farm and collect 3 wheat, 2 sugar cane.', 'Step 2: From your inventory or other agents get 3 milk buckets (already filled with milk).', 'Step 3: Get an egg from your inventory or other agents.', 'Step 4: Go to the crafting table and craft the sugarcane into sugar.', 'Step 5: Go to the crafting table and combine all ingredients (3 wheat, 2 sugar, 1 egg, and milk bucket) to make a cake.']\nRecipe for baked_potato:\n[\"Step 1: Go to the farm and collect 1 potato (search for 'potatoes' (not 'potato')).\", 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 2: Go to the furnace and bake the potato.']\n"}, "conversation": "Let's collaborate to make cake, baked_potato, "}, "multiagent_cooking_cake_baked_potato_partial_plan": {"type": "cooking", "recipes": {"cake": ["Step 1: Go to the farm and collect 3 wheat, 2 sugar cane.", "Step 2: From your inventory or other agents get 3 milk buckets (already filled with milk).", "Step 3: Get an egg from your inventory or other agents.", "Step 4: Go to the crafting table and craft the sugarcane into sugar.", "Step 5: Go to the crafting table and combine all ingredients (3 wheat, 2 sugar, 1 egg, and milk bucket) to make a cake."], "baked_potato": ["Step 1: Go to the farm and collect 1 potato (search for 'potatoes' (not 'potato')).", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 2: Go to the furnace and bake the potato."]}, "agent_count": 2, "target": {"cake": 1, "baked_potato": 1}, "initial_inventory": {"0": {"milk_bucket": 2, "coal": 1}, "1": {"milk_bucket": 1, "egg": 1}}, "goal": {"0": "Collaborate with other agents around you to make cake, baked_potato, ", "1": "Collaborate with other agents around you to make cake, baked_potato, "}, "conversation": "Let's collaborate to make cake, baked_potato, "}, "multiagent_cooking_cake_cooked_beef": {"type": "cooking", "recipes": {"cake": ["Step 1: Go to the farm and collect 3 wheat, 2 sugar cane.", "Step 2: From your inventory or other agents get 3 milk buckets (already filled with milk).", "Step 3: Get an egg from your inventory or other agents.", "Step 4: Go to the crafting table and craft the sugarcane into sugar.", "Step 5: Go to the crafting table and combine all ingredients (3 wheat, 2 sugar, 1 egg, and milk bucket) to make a cake."], "cooked_beef": ["Step 1: Kill a cow and pick up 1 beef that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the beef."]}, "agent_count": 2, "target": {"cake": 1, "cooked_beef": 1}, "initial_inventory": {"0": {"milk_bucket": 2, "coal": 1}, "1": {"milk_bucket": 1, "egg": 1}}, "goal": {"0": "Collaborate with other agents around you to make cake, cooked_beef, The recipes are as follows:\nRecipe for cake:\n['Step 1: Go to the farm and collect 3 wheat, 2 sugar cane.', 'Step 2: From your inventory or other agents get 3 milk buckets (already filled with milk).', 'Step 3: Get an egg from your inventory or other agents.', 'Step 4: Go to the crafting table and craft the sugarcane into sugar.', 'Step 5: Go to the crafting table and combine all ingredients (3 wheat, 2 sugar, 1 egg, and milk bucket) to make a cake.']\nRecipe for cooked_beef:\n['Step 1: Kill a cow and pick up 1 beef that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the beef.']\n", "1": "Collaborate with other agents around you to make cake, cooked_beef, The recipes are as follows:\nRecipe for cake:\n['Step 1: Go to the farm and collect 3 wheat, 2 sugar cane.', 'Step 2: From your inventory or other agents get 3 milk buckets (already filled with milk).', 'Step 3: Get an egg from your inventory or other agents.', 'Step 4: Go to the crafting table and craft the sugarcane into sugar.', 'Step 5: Go to the crafting table and combine all ingredients (3 wheat, 2 sugar, 1 egg, and milk bucket) to make a cake.']\nRecipe for cooked_beef:\n['Step 1: Kill a cow and pick up 1 beef that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the beef.']\n"}, "conversation": "Let's collaborate to make cake, cooked_beef, "}, "multiagent_cooking_cake_cooked_beef_partial_plan": {"type": "cooking", "recipes": {"cake": ["Step 1: Go to the farm and collect 3 wheat, 2 sugar cane.", "Step 2: From your inventory or other agents get 3 milk buckets (already filled with milk).", "Step 3: Get an egg from your inventory or other agents.", "Step 4: Go to the crafting table and craft the sugarcane into sugar.", "Step 5: Go to the crafting table and combine all ingredients (3 wheat, 2 sugar, 1 egg, and milk bucket) to make a cake."], "cooked_beef": ["Step 1: Kill a cow and pick up 1 beef that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the beef."]}, "agent_count": 2, "target": {"cake": 1, "cooked_beef": 1}, "initial_inventory": {"0": {"milk_bucket": 2, "coal": 1}, "1": {"milk_bucket": 1, "egg": 1}}, "goal": {"0": "Collaborate with other agents around you to make cake, cooked_beef, ", "1": "Collaborate with other agents around you to make cake, cooked_beef, "}, "conversation": "Let's collaborate to make cake, cooked_beef, "}, "multiagent_cooking_baked_potato_cooked_beef": {"type": "cooking", "recipes": {"baked_potato": ["Step 1: Go to the farm and collect 1 potato (search for 'potatoes' (not 'potato')).", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 2: Go to the furnace and bake the potato."], "cooked_beef": ["Step 1: Kill a cow and pick up 1 beef that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the beef."]}, "agent_count": 2, "target": {"baked_potato": 1, "cooked_beef": 1}, "initial_inventory": {"0": {"coal": 1}, "1": {"coal": 1}}, "goal": {"0": "Collaborate with other agents around you to make baked_potato, cooked_beef, The recipes are as follows:\nRecipe for baked_potato:\n[\"Step 1: Go to the farm and collect 1 potato (search for 'potatoes' (not 'potato')).\", 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 2: Go to the furnace and bake the potato.']\nRecipe for cooked_beef:\n['Step 1: Kill a cow and pick up 1 beef that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the beef.']\n", "1": "Collaborate with other agents around you to make baked_potato, cooked_beef, The recipes are as follows:\nRecipe for baked_potato:\n[\"Step 1: Go to the farm and collect 1 potato (search for 'potatoes' (not 'potato')).\", 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 2: Go to the furnace and bake the potato.']\nRecipe for cooked_beef:\n['Step 1: Kill a cow and pick up 1 beef that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the beef.']\n"}, "conversation": "Let's collaborate to make baked_potato, cooked_beef, "}, "multiagent_cooking_baked_potato_cooked_beef_partial_plan": {"type": "cooking", "recipes": {"baked_potato": ["Step 1: Go to the farm and collect 1 potato (search for 'potatoes' (not 'potato')).", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 2: Go to the furnace and bake the potato."], "cooked_beef": ["Step 1: Kill a cow and pick up 1 beef that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the beef."]}, "agent_count": 2, "target": {"baked_potato": 1, "cooked_beef": 1}, "initial_inventory": {"0": {"coal": 1}, "1": {"coal": 1}}, "goal": {"0": "Collaborate with other agents around you to make baked_potato, cooked_beef, ", "1": "Collaborate with other agents around you to make baked_potato, cooked_beef, "}, "conversation": "Let's collaborate to make baked_potato, cooked_beef, "}}