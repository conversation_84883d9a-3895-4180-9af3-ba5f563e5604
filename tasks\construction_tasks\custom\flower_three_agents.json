{"flower_three_agents": {"type": "construction", "goal": "Make a structure with the blueprint below", "conversation": "Let's share materials and make a structure with the blueprint", "agent_count": 3, "blueprint": {"materials": {"yellow_terracotta": 94, "brown_wool": 10, "black_wool": 60, "green_wool": 10, "orange_wool": 58, "red_wool": 44, "yellow_wool": 49, "magenta_wool": 66, "purple_wool": 73, "blue_wool": 87}, "levels": [{"level": 0, "coordinates": [-124, -60, 133], "placement": [["air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta"], ["air", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta"], ["air", "brown_wool", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "brown_wool", "yellow_terracotta", "brown_wool", "yellow_terracotta", "yellow_terracotta", "brown_wool", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "brown_wool", "brown_wool", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "brown_wool", "brown_wool", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "brown_wool", "yellow_terracotta", "yellow_terracotta", "brown_wool"], ["air", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "black_wool", "black_wool", "black_wool", "black_wool", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "black_wool", "black_wool", "black_wool", "black_wool", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "yellow_terracotta", "black_wool", "black_wool", "black_wool", "black_wool", "yellow_terracotta"], ["air", "green_wool", "black_wool", "black_wool", "black_wool", "black_wool", "black_wool", "black_wool", "black_wool", "black_wool", "green_wool", "green_wool", "green_wool", "black_wool", "black_wool", "black_wool", "black_wool", "black_wool", "black_wool", "black_wool", "black_wool", "black_wool", "green_wool", "black_wool", "black_wool", "black_wool", "black_wool", "black_wool", "black_wool", "black_wool"], ["air", "black_wool", "black_wool", "black_wool", "black_wool", "black_wool", "black_wool", "black_wool", "black_wool", "black_wool", "black_wool", "black_wool", "green_wool", "green_wool", "green_wool", "black_wool", "black_wool", "black_wool", "black_wool", "black_wool", "black_wool", "black_wool", "black_wool", "black_wool", "green_wool", "green_wool", "black_wool", "black_wool", "black_wool", "black_wool"], ["air", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool"], ["air", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool", "orange_wool"], ["air", "red_wool", "red_wool", "red_wool", "red_wool", "red_wool", "red_wool", "red_wool", "red_wool", "red_wool", "red_wool", "red_wool", "red_wool", "yellow_wool", "yellow_wool", "yellow_wool", "yellow_wool", "yellow_wool", "yellow_wool", "yellow_wool", "red_wool", "red_wool", "red_wool", "red_wool", "red_wool", "red_wool", "red_wool", "red_wool", "red_wool", "red_wool"], ["air", "red_wool", "red_wool", "red_wool", "red_wool", "red_wool", "red_wool", "red_wool", "red_wool", "red_wool", "red_wool", "red_wool", "red_wool", "yellow_wool", "yellow_wool", "yellow_wool", "yellow_wool", "yellow_wool", "yellow_wool", "yellow_wool", "red_wool", "red_wool", "red_wool", "red_wool", "red_wool", "red_wool", "red_wool", "red_wool", "red_wool", "red_wool"], ["air", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "yellow_wool", "yellow_wool", "yellow_wool", "yellow_wool", "yellow_wool", "yellow_wool", "yellow_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool"], ["air", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "yellow_wool", "yellow_wool", "yellow_wool", "yellow_wool", "yellow_wool", "yellow_wool", "yellow_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool"], ["air", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "yellow_wool", "yellow_wool", "yellow_wool", "yellow_wool", "yellow_wool", "yellow_wool", "yellow_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool", "magenta_wool"], ["air", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "yellow_wool", "yellow_wool", "yellow_wool", "yellow_wool", "yellow_wool", "yellow_wool", "yellow_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool"], ["air", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "yellow_wool", "yellow_wool", "yellow_wool", "yellow_wool", "yellow_wool", "yellow_wool", "yellow_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool"], ["air", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool", "purple_wool"], ["air", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool"], ["air", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool"], ["air", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool", "blue_wool"]]}, {"level": 1, "coordinates": [-124, -59, 133], "placement": [["air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air", "air"]]}]}, "initial_inventory": {"0": {"diamond_pickaxe": 1, "diamond_axe": 1, "diamond_shovel": 1, "yellow_terracotta": 94, "green_wool": 10, "yellow_wool": 49, "blue_wool": 87}, "1": {"diamond_pickaxe": 1, "diamond_axe": 1, "diamond_shovel": 1, "brown_wool": 10, "orange_wool": 58, "magenta_wool": 66}, "2": {"diamond_pickaxe": 1, "diamond_axe": 1, "diamond_shovel": 1, "black_wool": 60, "red_wool": 44, "purple_wool": 73}}}}