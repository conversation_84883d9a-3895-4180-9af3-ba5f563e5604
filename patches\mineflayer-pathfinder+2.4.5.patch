diff --git a/node_modules/mineflayer-pathfinder/index.js b/node_modules/mineflayer-pathfinder/index.js
index b38bd30..bf16a63 100644
--- a/node_modules/mineflayer-pathfinder/index.js
+++ b/node_modules/mineflayer-pathfinder/index.js
@@ -550,6 +550,7 @@ function inject (bot) {
             lockEquipItem.release()
             const refBlock = bot.blockAt(new Vec3(placingBlock.x, placingBlock.y, placingBlock.z), false)
             if (!lockPlaceBlock.tryAcquire()) return
+            bot.world.setBlockStateId(refBlock.position.offset(placingBlock.dx, placingBlock.dy, placingBlock.dz), 1)
             if (interactableBlocks.includes(refBlock.name)) {
               bot.setControlState('sneak', true)
             }
@@ -557,6 +558,7 @@ function inject (bot) {
               .then(function () {
                 // Dont release Sneak if the block placement was not successful
                 bot.setControlState('sneak', false)
+                bot.setControlState('jump', false)
                 if (bot.pathfinder.LOSWhenPlacingBlocks && placingBlock.returnPos) returningPos = placingBlock.returnPos.clone()
               })
               .catch(_ignoreError => {
