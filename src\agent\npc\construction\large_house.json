{"name": "large_house", "offset": -4, "blocks": [[["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", ""], ["", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", ""], ["", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", ""], ["", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", ""], ["", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", ""], ["", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", ""], ["", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""]], [["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", ""], ["", "cobblestone", "air", "air", "air", "air", "air", "air", "air", "cobblestone", ""], ["", "cobblestone", "air", "air", "air", "air", "air", "air", "air", "cobblestone", ""], ["", "cobblestone", "air", "air", "air", "air", "air", "air", "air", "cobblestone", ""], ["", "cobblestone", "planks", "air", "air", "air", "air", "air", "air", "cobblestone", ""], ["", "cobblestone", "planks", "air", "air", "air", "air", "air", "air", "cobblestone", ""], ["", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""]], [["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", ""], ["", "cobblestone", "air", "torch", "air", "air", "air", "torch", "air", "cobblestone", ""], ["", "cobblestone", "air", "air", "air", "air", "air", "air", "air", "cobblestone", ""], ["", "cobblestone", "air", "air", "air", "air", "air", "air", "air", "cobblestone", ""], ["", "cobblestone", "air", "air", "air", "air", "air", "air", "air", "cobblestone", ""], ["", "cobblestone", "planks", "torch", "air", "air", "air", "torch", "air", "cobblestone", ""], ["", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""]], [["", "", "", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "", "", ""], ["", "", "", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "", "", ""], ["", "", "", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "", "", ""], ["cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", ""], ["cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", ""], ["cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", ""], ["cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", ""], ["cobblestone", "cobblestone", "air", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "dirt"], ["cobblestone", "cobblestone", "air", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", ""], ["cobblestone", "cobblestone", "air", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", ""], ["", "", "", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", ""], ["", "", "", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", ""], ["", "", "", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", ""], ["", "", "", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", "cobblestone", ""]], [["", "", "", "log", "planks", "planks", "planks", "log", "", "", ""], ["", "", "", "planks", "furnace", "air", "crafting_table", "planks", "", "", ""], ["", "", "", "planks", "air", "air", "air", "planks", "", "", ""], ["log", "planks", "planks", "log", "planks", "air", "planks", "log", "planks", "log", ""], ["planks", "planks", "air", "air", "air", "air", "air", "air", "air", "planks", ""], ["planks", "planks", "air", "air", "air", "air", "air", "air", "air", "planks", ""], ["planks", "planks", "air", "air", "air", "air", "air", "air", "air", "door", "air"], ["planks", "planks", "air", "air", "air", "air", "air", "air", "air", "planks", ""], ["planks", "planks", "air", "air", "air", "air", "air", "air", "air", "planks", ""], ["log", "planks", "planks", "log", "planks", "planks", "air", "planks", "planks", "log", ""], ["", "", "", "planks", "air", "air", "air", "", "air", "planks", ""], ["", "", "", "planks", "chest", "air", "air", "bed", "", "planks", ""], ["", "", "", "planks", "chest", "air", "air", "", "air", "planks", ""], ["", "", "", "log", "planks", "planks", "planks", "planks", "planks", "log", ""]], [["", "", "", "log", "planks", "planks", "planks", "log", "", "", ""], ["", "", "", "planks", "air", "air", "air", "glass", "", "", ""], ["", "", "", "planks", "air", "air", "air", "glass", "", "", ""], ["log", "planks", "planks", "log", "planks", "air", "planks", "log", "planks", "log", ""], ["planks", "air", "air", "air", "air", "air", "air", "air", "air", "planks", ""], ["planks", "planks", "air", "air", "air", "air", "air", "air", "air", "planks", ""], ["planks", "planks", "air", "air", "air", "air", "air", "air", "air", "door", "air"], ["planks", "planks", "air", "air", "air", "air", "air", "air", "air", "planks", ""], ["planks", "planks", "air", "air", "air", "air", "air", "air", "air", "planks", ""], ["log", "planks", "planks", "log", "planks", "planks", "air", "planks", "planks", "log", ""], ["", "", "", "planks", "air", "air", "air", "air", "air", "planks", ""], ["", "", "", "planks", "air", "air", "air", "air", "air", "planks", ""], ["", "", "", "planks", "air", "air", "air", "air", "air", "planks", ""], ["", "", "", "log", "planks", "glass", "glass", "glass", "planks", "log", ""]], [["", "", "", "log", "planks", "planks", "planks", "log", "", "", ""], ["", "", "", "planks", "air", "air", "air", "glass", "", "", ""], ["", "", "", "planks", "torch", "air", "torch", "glass", "", "", ""], ["log", "planks", "planks", "log", "planks", "air", "planks", "log", "planks", "log", ""], ["planks", "air", "air", "torch", "air", "air", "air", "air", "air", "planks", ""], ["planks", "air", "air", "air", "air", "air", "air", "air", "torch", "planks", ""], ["planks", "planks", "air", "air", "air", "air", "air", "air", "air", "planks", ""], ["planks", "planks", "air", "air", "air", "air", "air", "air", "torch", "planks", ""], ["planks", "planks", "air", "torch", "air", "air", "air", "air", "air", "planks", ""], ["log", "planks", "planks", "log", "planks", "planks", "air", "planks", "planks", "log", ""], ["", "", "", "planks", "air", "torch", "air", "torch", "air", "planks", ""], ["", "", "", "planks", "air", "air", "air", "air", "air", "planks", ""], ["", "", "", "planks", "air", "air", "air", "air", "air", "planks", ""], ["", "", "", "log", "planks", "glass", "glass", "glass", "planks", "log", ""]], [["", "", "", "log", "log", "log", "log", "log", "", "", ""], ["", "", "", "log", "planks", "planks", "planks", "log", "", "", ""], ["", "", "", "log", "planks", "planks", "planks", "log", "", "", ""], ["log", "log", "log", "log", "log", "log", "log", "log", "log", "log", ""], ["log", "air", "planks", "planks", "planks", "planks", "planks", "planks", "planks", "log", ""], ["log", "air", "planks", "planks", "planks", "planks", "planks", "planks", "planks", "log", ""], ["log", "air", "planks", "planks", "planks", "planks", "planks", "planks", "planks", "log", ""], ["log", "planks", "planks", "planks", "planks", "planks", "planks", "planks", "planks", "log", ""], ["log", "planks", "planks", "planks", "planks", "planks", "planks", "planks", "planks", "log", ""], ["log", "log", "log", "log", "log", "log", "log", "log", "log", "log", ""], ["", "", "", "log", "planks", "planks", "planks", "planks", "planks", "log", ""], ["", "", "", "log", "planks", "planks", "planks", "planks", "planks", "log", ""], ["", "", "", "log", "planks", "planks", "planks", "planks", "planks", "log", ""], ["", "", "", "log", "log", "log", "log", "log", "log", "log", ""]], [["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "planks", "planks", "planks", "", "", "", ""], ["", "", "", "", "planks", "planks", "planks", "", "", "", ""], ["log", "planks", "planks", "log", "planks", "planks", "planks", "planks", "planks", "log", ""], ["planks", "air", "bookshelf", "bookshelf", "air", "air", "air", "air", "torch", "planks", ""], ["planks", "air", "air", "air", "air", "air", "air", "air", "air", "planks", ""], ["planks", "air", "air", "air", "air", "air", "air", "air", "air", "planks", ""], ["planks", "air", "air", "air", "air", "air", "air", "air", "air", "planks", ""], ["planks", "air", "air", "air", "air", "air", "air", "air", "torch", "planks", ""], ["log", "planks", "planks", "log", "planks", "planks", "planks", "planks", "planks", "log", ""], ["", "", "", "", "planks", "planks", "planks", "planks", "planks", "", ""], ["", "", "", "", "planks", "planks", "planks", "planks", "planks", "", ""], ["", "", "", "", "planks", "planks", "planks", "planks", "planks", "", ""], ["", "", "", "", "", "", "", "", "", "", ""]], [["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["log", "planks", "planks", "log", "glass", "glass", "glass", "glass", "glass", "log", ""], ["glass", "air", "bookshelf", "bookshelf", "air", "air", "air", "air", "air", "planks", ""], ["glass", "air", "air", "air", "air", "air", "air", "air", "air", "glass", ""], ["glass", "air", "air", "air", "air", "air", "air", "air", "air", "glass", ""], ["glass", "air", "air", "air", "air", "air", "air", "air", "air", "glass", ""], ["glass", "air", "air", "air", "air", "air", "air", "air", "air", "glass", ""], ["log", "planks", "planks", "log", "glass", "glass", "glass", "glass", "glass", "log", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""]], [["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["log", "planks", "planks", "log", "glass", "glass", "glass", "glass", "glass", "log", ""], ["glass", "air", "air", "torch", "air", "air", "air", "air", "air", "glass", ""], ["glass", "air", "air", "air", "air", "air", "air", "air", "air", "glass", ""], ["glass", "air", "air", "air", "air", "air", "air", "air", "air", "glass", ""], ["glass", "air", "air", "air", "air", "air", "air", "air", "air", "glass", ""], ["glass", "air", "air", "torch", "air", "air", "air", "air", "air", "glass", ""], ["log", "planks", "planks", "log", "glass", "glass", "glass", "glass", "glass", "log", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""]], [["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["log", "log", "log", "log", "log", "log", "log", "log", "log", "log", ""], ["log", "planks", "planks", "log", "planks", "planks", "planks", "planks", "planks", "log", ""], ["log", "planks", "planks", "log", "planks", "planks", "planks", "planks", "planks", "log", ""], ["log", "planks", "planks", "log", "planks", "planks", "planks", "planks", "planks", "log", ""], ["log", "planks", "planks", "log", "planks", "planks", "planks", "planks", "planks", "log", ""], ["log", "planks", "planks", "log", "planks", "planks", "planks", "planks", "planks", "log", ""], ["log", "log", "log", "log", "log", "log", "log", "log", "log", "log", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""]], [["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "planks", "planks", "planks", "planks", "planks", "", ""], ["", "", "", "", "planks", "planks", "planks", "planks", "planks", "", ""], ["", "", "", "", "planks", "planks", "planks", "planks", "planks", "", ""], ["", "", "", "", "planks", "planks", "planks", "planks", "planks", "", ""], ["", "", "", "", "planks", "planks", "planks", "planks", "planks", "", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""]], [["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "planks", "planks", "planks", "", "", ""], ["", "", "", "", "", "planks", "planks", "planks", "", "", ""], ["", "", "", "", "", "planks", "planks", "planks", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", ""]]]}