{"multiagent_crafting_requires_ctable_lectern_0_with_plan__depth_0_num_agents_4": {"goal": "Collaborate with other agents to craft an lectern", "conversation": "Let's work together to craft an lectern.", "initial_inventory": {"0": {"oak_slab": 1, "bookshelf": 1, "crafting_table": 1}, "1": {"oak_slab": 1}, "2": {"oak_slab": 1}, "3": {"oak_slab": 1}}, "agent_count": 4, "target": "lectern", "number_of_target": 1, "type": "techtree", "max_depth": 3, "depth": 0, "timeout": 500, "blocked_actions": {"0": [], "1": [], "2": [], "3": []}, "missing_items": [], "requires_crafting_table": true}, "multiagent_crafting_requires_ctable_polished_granite_0_with_plan__depth_0_num_agents_4": {"goal": "Collaborate with other agents to craft an polished_granite", "conversation": "Let's work together to craft an polished_granite.", "initial_inventory": {"0": {"granite": 1, "crafting_table": 1}, "1": {"granite": 1}, "2": {"granite": 1}, "3": {"granite": 1}}, "agent_count": 4, "target": "polished_granite", "number_of_target": 1, "type": "techtree", "max_depth": 2, "depth": 0, "timeout": 500, "blocked_actions": {"0": [], "1": [], "2": [], "3": []}, "missing_items": [], "requires_crafting_table": true}, "multiagent_crafting_requires_ctable_smoker_0_with_plan__depth_0_num_agents_4": {"goal": "Collaborate with other agents to craft an smoker", "conversation": "Let's work together to craft an smoker.", "initial_inventory": {"0": {"dark_oak_log": 1, "furnace": 1, "crafting_table": 1}, "1": {"dark_oak_log": 1}, "2": {"dark_oak_log": 1}, "3": {"dark_oak_log": 1}}, "agent_count": 4, "target": "smoker", "number_of_target": 1, "type": "techtree", "max_depth": 1, "depth": 0, "timeout": 500, "blocked_actions": {"0": [], "1": [], "2": [], "3": []}, "missing_items": [], "requires_crafting_table": true}, "multiagent_crafting_requires_ctable_white_banner_0_with_plan__depth_0_num_agents_4": {"goal": "Collaborate with other agents to craft an white_banner", "conversation": "Let's work together to craft an white_banner.", "initial_inventory": {"0": {"white_wool": 1, "stick": 1, "crafting_table": 1}, "1": {"white_wool": 3}, "2": {"white_wool": 1}, "3": {"white_wool": 1}}, "agent_count": 4, "target": "white_banner", "number_of_target": 1, "type": "techtree", "max_depth": 3, "depth": 0, "timeout": 500, "blocked_actions": {"0": [], "1": [], "2": [], "3": []}, "missing_items": [], "requires_crafting_table": true}, "multiagent_crafting_requires_ctable_blue_banner_0_with_plan__depth_0_num_agents_4": {"goal": "Collaborate with other agents to craft an blue_banner", "conversation": "Let's work together to craft an blue_banner.", "initial_inventory": {"0": {"blue_wool": 1, "stick": 1, "crafting_table": 1}, "1": {"blue_wool": 1}, "2": {"blue_wool": 1}, "3": {"blue_wool": 3}}, "agent_count": 4, "target": "blue_banner", "number_of_target": 1, "type": "techtree", "max_depth": 2, "depth": 0, "timeout": 500, "blocked_actions": {"0": [], "1": [], "2": [], "3": []}, "missing_items": [], "requires_crafting_table": true}, "multiagent_crafting_requires_ctable_red_stained_glass_0_with_plan__depth_0_num_agents_4": {"goal": "Collaborate with other agents to craft an red_stained_glass", "conversation": "Let's work together to craft an red_stained_glass.", "initial_inventory": {"0": {"glass": 2, "red_dye": 1, "crafting_table": 1}, "1": {"glass": 2}, "2": {"glass": 2}, "3": {"glass": 2}}, "agent_count": 4, "target": "red_stained_glass", "number_of_target": 1, "type": "techtree", "max_depth": 1, "depth": 0, "timeout": 500, "blocked_actions": {"0": [], "1": [], "2": [], "3": []}, "missing_items": [], "requires_crafting_table": true}, "multiagent_crafting_requires_ctable_white_banner_1_with_plan__depth_0_num_agents_4": {"goal": "Collaborate with other agents to craft an white_banner", "conversation": "Let's work together to craft an white_banner.", "initial_inventory": {"0": {"white_wool": 1, "stick": 1, "crafting_table": 1}, "1": {"white_wool": 3}, "2": {"white_wool": 1}, "3": {"white_wool": 1}}, "agent_count": 4, "target": "white_banner", "number_of_target": 1, "type": "techtree", "max_depth": 3, "depth": 0, "timeout": 500, "blocked_actions": {"0": ["!getCraftingPlan"], "1": [], "2": [], "3": []}, "missing_items": [], "requires_crafting_table": true}, "multiagent_crafting_requires_ctable_cyan_stained_glass_3_with_plan__depth_0_num_agents_4": {"goal": "Collaborate with other agents to craft an cyan_stained_glass", "conversation": "Let's work together to craft an cyan_stained_glass.", "initial_inventory": {"0": {"glass": 2, "cyan_dye": 1, "crafting_table": 1}, "1": {"glass": 2}, "2": {"glass": 2}, "3": {"glass": 2}}, "agent_count": 4, "target": "cyan_stained_glass", "number_of_target": 1, "type": "techtree", "max_depth": 2, "depth": 0, "timeout": 500, "blocked_actions": {"0": ["!getCraftingPlan"], "1": ["!getCraftingPlan"], "2": ["!getCraftingPlan"], "3": []}, "missing_items": [], "requires_crafting_table": true}, "multiagent_crafting_requires_ctable_waxed_oxidized_cut_copper_1_with_plan__depth_0_num_agents_4": {"goal": "Collaborate with other agents to craft an waxed_oxidized_cut_copper", "conversation": "Let's work together to craft an waxed_oxidized_cut_copper.", "initial_inventory": {"0": {"waxed_oxidized_copper": 1, "crafting_table": 1}, "1": {"waxed_oxidized_copper": 1}, "2": {"waxed_oxidized_copper": 1}, "3": {"waxed_oxidized_copper": 1}}, "agent_count": 4, "target": "waxed_oxidized_cut_copper", "number_of_target": 1, "type": "techtree", "max_depth": 1, "depth": 0, "timeout": 500, "blocked_actions": {"0": ["!getCraftingPlan"], "1": [], "2": [], "3": []}, "missing_items": [], "requires_crafting_table": true}, "multiagent_crafting_requires_ctable_blue_banner_3_with_plan__depth_0_num_agents_4": {"goal": "Collaborate with other agents to craft an blue_banner", "conversation": "Let's work together to craft an blue_banner.", "initial_inventory": {"0": {"blue_wool": 1, "stick": 1, "crafting_table": 1}, "1": {"blue_wool": 1}, "2": {"blue_wool": 1}, "3": {"blue_wool": 3}}, "agent_count": 4, "target": "blue_banner", "number_of_target": 1, "type": "techtree", "max_depth": 2, "depth": 0, "timeout": 500, "blocked_actions": {"0": ["!getCraftingPlan"], "1": ["!getCraftingPlan"], "2": ["!getCraftingPlan"], "3": []}, "missing_items": [], "requires_crafting_table": true}, "multiagent_crafting_requires_ctable_white_banner_2_with_plan__depth_0_num_agents_4": {"goal": "Collaborate with other agents to craft an white_banner", "conversation": "Let's work together to craft an white_banner.", "initial_inventory": {"0": {"white_wool": 1, "stick": 1, "crafting_table": 1}, "1": {"white_wool": 3}, "2": {"white_wool": 1}, "3": {"white_wool": 1}}, "agent_count": 4, "target": "white_banner", "number_of_target": 1, "type": "techtree", "max_depth": 3, "depth": 0, "timeout": 500, "blocked_actions": {"0": ["!getCraftingPlan"], "1": ["!getCraftingPlan"], "2": [], "3": []}, "missing_items": [], "requires_crafting_table": true}, "multiagent_crafting_requires_ctable_smoker_3_with_plan__depth_0_num_agents_4": {"goal": "Collaborate with other agents to craft an smoker", "conversation": "Let's work together to craft an smoker.", "initial_inventory": {"0": {"dark_oak_log": 1, "furnace": 1, "crafting_table": 1}, "1": {"dark_oak_log": 1}, "2": {"dark_oak_log": 1}, "3": {"dark_oak_log": 1}}, "agent_count": 4, "target": "smoker", "number_of_target": 1, "type": "techtree", "max_depth": 1, "depth": 0, "timeout": 500, "blocked_actions": {"0": ["!getCraftingPlan"], "1": ["!getCraftingPlan"], "2": ["!getCraftingPlan"], "3": []}, "missing_items": [], "requires_crafting_table": true}, "multiagent_crafting_requires_ctable_black_banner_1_with_plan__depth_0_num_agents_4": {"goal": "Collaborate with other agents to craft an black_banner", "conversation": "Let's work together to craft an black_banner.", "initial_inventory": {"0": {"black_wool": 1, "stick": 1, "crafting_table": 1}, "1": {"black_wool": 1}, "2": {"black_wool": 1}, "3": {"black_wool": 3}}, "agent_count": 4, "target": "black_banner", "number_of_target": 1, "type": "techtree", "max_depth": 2, "depth": 0, "timeout": 500, "blocked_actions": {"0": ["!getCraftingPlan"], "1": [], "2": [], "3": []}, "missing_items": [], "requires_crafting_table": true}, "multiagent_crafting_requires_ctable_cyan_stained_glass_0_with_plan__depth_1_num_agents_4": {"goal": "Collaborate with other agents to craft an cyan_stained_glass", "conversation": "Let's work together to craft an cyan_stained_glass.", "initial_inventory": {"0": {"glass": 2, "blue_dye": 1, "crafting_table": 1}, "1": {"glass": 2, "green_dye": 1}, "2": {"glass": 2}, "3": {"glass": 2}}, "agent_count": 4, "target": "cyan_stained_glass", "number_of_target": 1, "type": "techtree", "max_depth": 2, "depth": 1, "timeout": 500, "blocked_actions": {"0": [], "1": [], "2": [], "3": []}, "missing_items": [], "requires_crafting_table": true}, "multiagent_crafting_requires_ctable_polished_granite_0_with_plan__depth_1_num_agents_4": {"goal": "Collaborate with other agents to craft an polished_granite", "conversation": "Let's work together to craft an polished_granite.", "initial_inventory": {"0": {"diorite": 1, "quartz": 1, "crafting_table": 1}, "1": {"diorite": 1, "quartz": 1}, "2": {"diorite": 1, "quartz": 1}, "3": {"diorite": 1, "quartz": 1}}, "agent_count": 4, "target": "polished_granite", "number_of_target": 1, "type": "techtree", "max_depth": 2, "depth": 1, "timeout": 500, "blocked_actions": {"0": [], "1": [], "2": [], "3": []}, "missing_items": [], "requires_crafting_table": true}, "multiagent_crafting_requires_ctable_cyan_banner_0_with_plan__depth_1_num_agents_4": {"goal": "Collaborate with other agents to craft an cyan_banner", "conversation": "Let's work together to craft an cyan_banner.", "initial_inventory": {"0": {"cyan_dye": 1, "black_wool": 1, "oak_planks": 2, "crafting_table": 1}, "1": {"cyan_dye": 1, "black_wool": 1}, "2": {"cyan_dye": 3, "black_wool": 3}, "3": {"cyan_dye": 1, "black_wool": 1}}, "agent_count": 4, "target": "cyan_banner", "number_of_target": 1, "type": "techtree", "max_depth": 3, "depth": 1, "timeout": 500, "blocked_actions": {"0": [], "1": [], "2": [], "3": []}, "missing_items": [], "requires_crafting_table": true}, "multiagent_crafting_requires_ctable_white_banner_0_with_plan__depth_1_num_agents_4": {"goal": "Collaborate with other agents to craft an white_banner", "conversation": "Let's work together to craft an white_banner.", "initial_inventory": {"0": {"white_dye": 3, "black_wool": 1, "oak_planks": 2, "crafting_table": 1}, "1": {"white_dye": 1, "black_wool": 1}, "2": {"white_dye": 1, "black_wool": 1}, "3": {"white_dye": 1, "black_wool": 3}}, "agent_count": 4, "target": "white_banner", "number_of_target": 1, "type": "techtree", "max_depth": 3, "depth": 1, "timeout": 500, "blocked_actions": {"0": [], "1": [], "2": [], "3": []}, "missing_items": [], "requires_crafting_table": true}, "multiagent_crafting_requires_ctable_bookshelf_2_with_plan__depth_1_num_agents_4": {"goal": "Collaborate with other agents to craft an bookshelf", "conversation": "Let's work together to craft an bookshelf.", "initial_inventory": {"0": {"oak_log": 2, "paper": 3, "crafting_table": 1}, "1": {"paper": 2, "leather": 3}, "2": {"paper": 2}, "3": {"paper": 2}}, "agent_count": 4, "target": "bookshelf", "number_of_target": 1, "type": "techtree", "max_depth": 2, "depth": 1, "timeout": 500, "blocked_actions": {"0": ["!getCraftingPlan"], "1": ["!getCraftingPlan"], "2": [], "3": []}, "missing_items": [], "requires_crafting_table": true}, "multiagent_crafting_requires_ctable_spectral_arrow_1_with_plan__depth_1_num_agents_4": {"goal": "Collaborate with other agents to craft an spectral_arrow", "conversation": "Let's work together to craft an spectral_arrow.", "initial_inventory": {"0": {"glowstone_dust": 1, "flint": 1, "crafting_table": 1}, "1": {"glowstone_dust": 1, "stick": 1}, "2": {"glowstone_dust": 1, "feather": 1}, "3": {"glowstone_dust": 1}}, "agent_count": 4, "target": "spectral_arrow", "number_of_target": 1, "type": "techtree", "max_depth": 3, "depth": 1, "timeout": 500, "blocked_actions": {"0": ["!getCraftingPlan"], "1": [], "2": [], "3": []}, "missing_items": [], "requires_crafting_table": true}, "multiagent_crafting_requires_ctable_chest_minecart_2_with_plan__depth_1_num_agents_4": {"goal": "Collaborate with other agents to craft an chest_minecart", "conversation": "Let's work together to craft an chest_minecart.", "initial_inventory": {"0": {"oak_planks": 2, "iron_ingot": 1, "crafting_table": 1}, "1": {"oak_planks": 2, "iron_ingot": 2}, "2": {"oak_planks": 2, "iron_ingot": 1}, "3": {"oak_planks": 2, "iron_ingot": 1}}, "agent_count": 4, "target": "chest_minecart", "number_of_target": 1, "type": "techtree", "max_depth": 2, "depth": 1, "timeout": 500, "blocked_actions": {"0": ["!getCraftingPlan"], "1": ["!getCraftingPlan"], "2": [], "3": []}, "missing_items": [], "requires_crafting_table": true}, "multiagent_crafting_requires_ctable_chest_minecart_3_with_plan__depth_1_num_agents_4": {"goal": "Collaborate with other agents to craft an chest_minecart", "conversation": "Let's work together to craft an chest_minecart.", "initial_inventory": {"0": {"oak_planks": 2, "iron_ingot": 1, "crafting_table": 1}, "1": {"oak_planks": 2, "iron_ingot": 2}, "2": {"oak_planks": 2, "iron_ingot": 1}, "3": {"oak_planks": 2, "iron_ingot": 1}}, "agent_count": 4, "target": "chest_minecart", "number_of_target": 1, "type": "techtree", "max_depth": 2, "depth": 1, "timeout": 500, "blocked_actions": {"0": ["!getCraftingPlan"], "1": ["!getCraftingPlan"], "2": ["!getCraftingPlan"], "3": []}, "missing_items": [], "requires_crafting_table": true}, "multiagent_crafting_requires_ctable_blue_banner_3_with_plan__depth_1_num_agents_4": {"goal": "Collaborate with other agents to craft an blue_banner", "conversation": "Let's work together to craft an blue_banner.", "initial_inventory": {"0": {"blue_wool": 1, "oak_planks": 2, "crafting_table": 1}, "1": {"blue_wool": 3}, "2": {"blue_wool": 1}, "3": {"blue_wool": 1}}, "agent_count": 4, "target": "blue_banner", "number_of_target": 1, "type": "techtree", "max_depth": 2, "depth": 1, "timeout": 500, "blocked_actions": {"0": ["!getCraftingPlan"], "1": ["!getCraftingPlan"], "2": ["!getCraftingPlan"], "3": []}, "missing_items": [], "requires_crafting_table": true}, "multiagent_crafting_requires_ctable_cyan_banner_0_with_plan__depth_2_num_agents_4": {"goal": "Collaborate with other agents to craft an cyan_banner", "conversation": "Let's work together to craft an cyan_banner.", "initial_inventory": {"0": {"blue_dye": 3, "black_wool": 1, "crafting_table": 1}, "1": {"green_dye": 3, "black_wool": 3}, "2": {"black_wool": 1, "oak_log": 1}, "3": {"black_wool": 1}}, "agent_count": 4, "target": "cyan_banner", "number_of_target": 1, "type": "techtree", "max_depth": 3, "depth": 2, "timeout": 500, "blocked_actions": {"0": [], "1": [], "2": [], "3": []}, "missing_items": [], "requires_crafting_table": true}, "multiagent_crafting_requires_ctable_cyan_banner_2_with_plan__depth_2_num_agents_4": {"goal": "Collaborate with other agents to craft an cyan_banner", "conversation": "Let's work together to craft an cyan_banner.", "initial_inventory": {"0": {"blue_dye": 3, "black_wool": 1, "crafting_table": 1}, "1": {"green_dye": 3, "black_wool": 3}, "2": {"black_wool": 1, "oak_log": 1}, "3": {"black_wool": 1}}, "agent_count": 4, "target": "cyan_banner", "number_of_target": 1, "type": "techtree", "max_depth": 3, "depth": 2, "timeout": 500, "blocked_actions": {"0": ["!getCraftingPlan"], "1": ["!getCraftingPlan"], "2": [], "3": []}, "missing_items": [], "requires_crafting_table": true}, "multiagent_crafting_requires_ctable_spectral_arrow_2_with_plan__depth_2_num_agents_4": {"goal": "Collaborate with other agents to craft an spectral_arrow", "conversation": "Let's work together to craft an spectral_arrow.", "initial_inventory": {"0": {"glowstone_dust": 1, "flint": 1, "crafting_table": 1}, "1": {"glowstone_dust": 1, "oak_planks": 2}, "2": {"glowstone_dust": 1, "feather": 1}, "3": {"glowstone_dust": 1}}, "agent_count": 4, "target": "spectral_arrow", "number_of_target": 1, "type": "techtree", "max_depth": 3, "depth": 2, "timeout": 500, "blocked_actions": {"0": ["!getCraftingPlan"], "1": ["!getCraftingPlan"], "2": [], "3": []}, "missing_items": [], "requires_crafting_table": true}}