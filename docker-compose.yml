version: '3.8'

services:
  app:
    image: node:latest
    working_dir: /app
    volumes:
      - .:/app
    command: node main.js
    ports:
      - "3000-3003:3000-3003" # see the view from the camera mounted on your bot head: http://localhost:3000/

  viaproxy: #use this service to connect to an unsupported minecraft server versions. more info: ./services/viaproxy/README.md
    image: ghcr.io/viaversion/viaproxy:latest
    volumes:
      - ./services/viaproxy:/app/run
    ports:
      - "25568:25568"
    profiles:
      - viaproxy
