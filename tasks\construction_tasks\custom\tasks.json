{"small_church": {"type": "construction", "goal": "Make a house with the blueprint below", "conversation": "Let's share materials and make a house with the blueprint", "agent_count": 2, "blueprint": {"materials": {"oak_planks": 140, "stone_bricks": 108, "oak_door": 2, "oak_stairs": 16, "quartz_block": 1, "glass_pane": 12, "torch": 4}, "levels": [{"level": 0, "coordinates": [-18, -60, 30], "placement": [["oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks"], ["oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks"], ["oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks"], ["oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks"], ["oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks"], ["oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks"], ["oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks"], ["oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks"], ["oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks"], ["oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks"]]}, {"level": 1, "coordinates": [-18, -60, 30], "placement": [["air", "stone_bricks", "stone_bricks", "oak_door", "stone_bricks", "air", "air"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks"], ["stone_bricks", "oak_stairs", "oak_stairs", "air", "oak_stairs", "oak_stairs", "stone_bricks"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks"], ["stone_bricks", "oak_stairs", "oak_stairs", "air", "oak_stairs", "oak_stairs", "stone_bricks"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks"], ["stone_bricks", "oak_stairs", "oak_stairs", "air", "oak_stairs", "oak_stairs", "stone_bricks"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks"], ["stone_bricks", "oak_stairs", "oak_stairs", "air", "oak_stairs", "oak_stairs", "stone_bricks"], ["stone_bricks", "air", "air", "quartz_block", "air", "air", "stone_bricks"]]}, {"level": 2, "coordinates": [-18, -60, 30], "placement": [["stone_bricks", "glass_pane", "stone_bricks", "oak_door", "stone_bricks", "glass_pane", "stone_bricks"], ["glass_pane", "air", "air", "air", "air", "air", "glass_pane"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks"], ["glass_pane", "air", "air", "air", "air", "air", "glass_pane"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks"], ["glass_pane", "air", "air", "air", "air", "air", "glass_pane"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks"], ["glass_pane", "air", "air", "air", "air", "air", "glass_pane"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks"], ["glass_pane", "air", "air", "air", "air", "air", "glass_pane"]]}, {"level": 3, "coordinates": [-18, -60, 30], "placement": [["stone_bricks", "stone_bricks", "stone_bricks", "stone_bricks", "stone_bricks", "stone_bricks", "stone_bricks"], ["stone_bricks", "torch", "air", "air", "air", "torch", "stone_bricks"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks"], ["stone_bricks", "torch", "air", "air", "air", "torch", "stone_bricks"]]}, {"level": 4, "coordinates": [-18, -60, 30], "placement": [["stone_bricks", "stone_bricks", "stone_bricks", "stone_bricks", "stone_bricks", "stone_bricks", "stone_bricks"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks"]]}, {"level": 5, "coordinates": [-18, -60, 30], "placement": [["stone_bricks", "stone_bricks", "stone_bricks", "stone_bricks", "stone_bricks", "stone_bricks", "stone_bricks"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks"]]}, {"level": 6, "coordinates": [-18, -60, 30], "placement": [["oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks"], ["oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks"], ["oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks"], ["oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks"], ["oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks"], ["oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks"], ["oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks"], ["oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks"], ["oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks"], ["oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks"]]}, {"level": 7, "coordinates": [-18, -60, 30], "placement": [["air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air"]]}, {"level": 8, "coordinates": [-18, -60, 30], "placement": [["air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air"]]}, {"level": 9, "coordinates": [-18, -60, 30], "placement": [["air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air"]]}]}, "initial_inventory": {"0": {"oak_planks": 140, "oak_door": 2, "quartz_block": 1, "torch": 4}, "1": {"stone_bricks": 108, "oak_stairs": 16, "glass_pane": 12}}}}