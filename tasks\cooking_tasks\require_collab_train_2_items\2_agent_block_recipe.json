{"multiagent_cooking_1_cooked_porkchop_1_cooked_rabbit_blocked_access_0": {"conversation": "Let's collaborate to make cooked_rabbit and cooked_porkchop.", "agent_count": 2, "target": {"cooked_rabbit": 1, "cooked_porkchop": 1}, "type": "cooking", "timeout": 300, "recipes": {"cooked_rabbit": ["Step 1: Kill a rabbit and pick up 1 raw rabbit that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 2: Go to furnace and use it to cook the raw rabbit."], "cooked_porkchop": ["Step 1: Kill a pig and pick up 1 porkchop that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the porkchop."]}, "blocked_access_to_recipe": ["0"], "goal": {"0": "Collaborate with agents around you to make 1 cooked_rabbit, 1 cooked_porkchop. ", "1": "Collaborate with agents around you to make 1 cooked_rabbit, 1 cooked_porkchop. Recipe for cooked_rabbit:\n['Step 1: Kill a rabbit and pick up 1 raw rabbit that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 2: Go to furnace and use it to cook the raw rabbit.']Recipe for cooked_porkchop:\n['Step 1: Kill a pig and pick up 1 porkchop that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the porkchop.']"}, "difficulty_metrics": {"total_recipe_steps": 4, "blocked_agents_count": 1, "unique_target_items": 2, "overall_difficulty_score": 4, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"coal": 1}, "1": {"coal": 1}}}, "multiagent_cooking_1_cookie_1_mushroom_stew": {"conversation": "Let's collaborate to make cookie and mushroom_stew.", "agent_count": 2, "target": {"cookie": 1, "mushroom_stew": 1}, "type": "cooking", "timeout": 300, "recipes": {"cookie": ["Step 1: Go to the farm and collect 2 wheat.", "Step 2: Get 1 cocoa bean from your inventory or other agents.", "Step 3: Go to the crafting table and combine the wheat and cocoa bean to craft a cookie."], "mushroom_stew": ["Step 1: Go to the farm and collect 1 red mushroom and 1 brown mushroom.", "Step 2: From your inventory or other agents get a bowl.", "Step 3: Go to the crafting table and combine both the mushrooms and bowl to make mushroom stew."]}, "blocked_access_to_recipe": [], "goal": {"0": "Collaborate with agents around you to make 1 cookie, 1 mushroom_stew. Recipe for cookie:\n['Step 1: Go to the farm and collect 2 wheat.', 'Step 2: Get 1 cocoa bean from your inventory or other agents.', 'Step 3: Go to the crafting table and combine the wheat and cocoa bean to craft a cookie.']Recipe for mushroom_stew:\n['Step 1: Go to the farm and collect 1 red mushroom and 1 brown mushroom.', 'Step 2: From your inventory or other agents get a bowl.', 'Step 3: Go to the crafting table and combine both the mushrooms and bowl to make mushroom stew.']", "1": "Collaborate with agents around you to make 1 cookie, 1 mushroom_stew. Recipe for cookie:\n['Step 1: Go to the farm and collect 2 wheat.', 'Step 2: Get 1 cocoa bean from your inventory or other agents.', 'Step 3: Go to the crafting table and combine the wheat and cocoa bean to craft a cookie.']Recipe for mushroom_stew:\n['Step 1: Go to the farm and collect 1 red mushroom and 1 brown mushroom.', 'Step 2: From your inventory or other agents get a bowl.', 'Step 3: Go to the crafting table and combine both the mushrooms and bowl to make mushroom stew.']"}, "difficulty_metrics": {"total_recipe_steps": 6, "blocked_agents_count": 0, "unique_target_items": 2, "overall_difficulty_score": 4, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"cocoa_beans": 1}, "1": {"bowl": 1}}}, "multiagent_cooking_1_beetroot_soup_1_golden_carrot_blocked_access_1": {"conversation": "Let's collaborate to make beetroot_soup and golden_carrot.", "agent_count": 2, "target": {"beetroot_soup": 1, "golden_carrot": 1}, "type": "cooking", "timeout": 300, "recipes": {"beetroot_soup": ["Step 1: Go to the farm and collect 6 beetroot.", "Step 2: From your inventory or other agents get a bowl.", "Step 3: Go to the crafting table and combine the 6 beetroot and 1 bowl to make beetroot soup."], "golden_carrot": ["Step 1: Go to the farm and collect 1 carrot.", "Step 2: Go to the chest and collect gold ingots and convert them to gold nuggets.", "Step 3: Go to the crafting table and surround the carrot with gold nuggets to create a golden carrot."]}, "blocked_access_to_recipe": ["1"], "goal": {"0": "Collaborate with agents around you to make 1 beetroot_soup, 1 golden_carrot. Recipe for beetroot_soup:\n['Step 1: Go to the farm and collect 6 beetroot.', 'Step 2: From your inventory or other agents get a bowl.', 'Step 3: Go to the crafting table and combine the 6 beetroot and 1 bowl to make beetroot soup.']Recipe for golden_carrot:\n['Step 1: Go to the farm and collect 1 carrot.', 'Step 2: Go to the chest and collect gold ingots and convert them to gold nuggets.', 'Step 3: Go to the crafting table and surround the carrot with gold nuggets to create a golden carrot.']", "1": "Collaborate with agents around you to make 1 beetroot_soup, 1 golden_carrot. "}, "difficulty_metrics": {"total_recipe_steps": 6, "blocked_agents_count": 1, "unique_target_items": 2, "overall_difficulty_score": 5, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"bowl": 1, "gold_ingot": 4}, "1": {"gold_ingot": 4}}}, "multiagent_cooking_1_beetroot_soup_1_cooked_mutton": {"conversation": "Let's collaborate to make cooked_mutton and beetroot_soup.", "agent_count": 2, "target": {"cooked_mutton": 1, "beetroot_soup": 1}, "type": "cooking", "timeout": 300, "recipes": {"cooked_mutton": ["Step 1: Kill a sheep and pick up 1 mutton that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the mutton."], "beetroot_soup": ["Step 1: Go to the farm and collect 6 beetroot.", "Step 2: From your inventory or other agents get a bowl.", "Step 3: Go to the crafting table and combine the 6 beetroot and 1 bowl to make beetroot soup."]}, "blocked_access_to_recipe": [], "goal": {"0": "Collaborate with agents around you to make 1 cooked_mutton, 1 beetroot_soup. Recipe for cooked_mutton:\n['Step 1: Kill a sheep and pick up 1 mutton that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the mutton.']Recipe for beetroot_soup:\n['Step 1: Go to the farm and collect 6 beetroot.', 'Step 2: From your inventory or other agents get a bowl.', 'Step 3: Go to the crafting table and combine the 6 beetroot and 1 bowl to make beetroot soup.']", "1": "Collaborate with agents around you to make 1 cooked_mutton, 1 beetroot_soup. Recipe for cooked_mutton:\n['Step 1: Kill a sheep and pick up 1 mutton that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the mutton.']Recipe for beetroot_soup:\n['Step 1: Go to the farm and collect 6 beetroot.', 'Step 2: From your inventory or other agents get a bowl.', 'Step 3: Go to the crafting table and combine the 6 beetroot and 1 bowl to make beetroot soup.']"}, "difficulty_metrics": {"total_recipe_steps": 5, "blocked_agents_count": 0, "unique_target_items": 2, "overall_difficulty_score": 4, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"coal": 1}, "1": {"bowl": 1}}}, "multiagent_cooking_1_cooked_porkchop_1_pumpkin_pie": {"conversation": "Let's collaborate to make pumpkin_pie and cooked_porkchop.", "agent_count": 2, "target": {"pumpkin_pie": 1, "cooked_porkchop": 1}, "type": "cooking", "timeout": 300, "recipes": {"pumpkin_pie": ["Step 1: Go to the farm and collect 1 pumpkin and 1 sugar cane.", "Step 2: Get 1 egg from your inventory or other bots", "Step 3: Go to the crafting table and craft the sugar cane into sugar.", "Step 4: Go to the crafting table and combine the pumpkin, egg, and sugar to make a pumpkin pie."], "cooked_porkchop": ["Step 1: Kill a pig and pick up 1 porkchop that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the porkchop."]}, "blocked_access_to_recipe": [], "goal": {"0": "Collaborate with agents around you to make 1 pumpkin_pie, 1 cooked_porkchop. Recipe for pumpkin_pie:\n['Step 1: Go to the farm and collect 1 pumpkin and 1 sugar cane.', 'Step 2: Get 1 egg from your inventory or other bots', 'Step 3: Go to the crafting table and craft the sugar cane into sugar.', 'Step 4: Go to the crafting table and combine the pumpkin, egg, and sugar to make a pumpkin pie.']Recipe for cooked_porkchop:\n['Step 1: Kill a pig and pick up 1 porkchop that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the porkchop.']", "1": "Collaborate with agents around you to make 1 pumpkin_pie, 1 cooked_porkchop. Recipe for pumpkin_pie:\n['Step 1: Go to the farm and collect 1 pumpkin and 1 sugar cane.', 'Step 2: Get 1 egg from your inventory or other bots', 'Step 3: Go to the crafting table and craft the sugar cane into sugar.', 'Step 4: Go to the crafting table and combine the pumpkin, egg, and sugar to make a pumpkin pie.']Recipe for cooked_porkchop:\n['Step 1: Kill a pig and pick up 1 porkchop that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the porkchop.']"}, "difficulty_metrics": {"total_recipe_steps": 6, "blocked_agents_count": 0, "unique_target_items": 2, "overall_difficulty_score": 4, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"egg": 1}, "1": {"coal": 1}}}, "multiagent_cooking_1_cooked_chicken_1_suspicious_stew": {"conversation": "Let's collaborate to make cooked_chicken and suspicious_stew.", "agent_count": 2, "target": {"cooked_chicken": 1, "suspicious_stew": 1}, "type": "cooking", "timeout": 300, "recipes": {"cooked_chicken": ["Step 1: Kill a chicken and pick up 1 raw chicken that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the raw chicken."], "suspicious_stew": ["Step 1: Go to the farm and collect 1 red mushroom, 1 brown mushroom.", "Step 2: From your inventory or other agents get a bowl and 1 dandelion", "Step 3: Go to the crafting table and combine the mushrooms, dandelion, and bowl to make suspicious stew."]}, "blocked_access_to_recipe": [], "goal": {"0": "Collaborate with agents around you to make 1 cooked_chicken, 1 suspicious_stew. Recipe for cooked_chicken:\n['Step 1: Kill a chicken and pick up 1 raw chicken that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the raw chicken.']Recipe for suspicious_stew:\n['Step 1: Go to the farm and collect 1 red mushroom, 1 brown mushroom.', 'Step 2: From your inventory or other agents get a bowl and 1 dandelion', 'Step 3: Go to the crafting table and combine the mushrooms, dandelion, and bowl to make suspicious stew.']", "1": "Collaborate with agents around you to make 1 cooked_chicken, 1 suspicious_stew. Recipe for cooked_chicken:\n['Step 1: Kill a chicken and pick up 1 raw chicken that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the raw chicken.']Recipe for suspicious_stew:\n['Step 1: Go to the farm and collect 1 red mushroom, 1 brown mushroom.', 'Step 2: From your inventory or other agents get a bowl and 1 dandelion', 'Step 3: Go to the crafting table and combine the mushrooms, dandelion, and bowl to make suspicious stew.']"}, "difficulty_metrics": {"total_recipe_steps": 5, "blocked_agents_count": 0, "unique_target_items": 2, "overall_difficulty_score": 4, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"coal": 1, "dandelion": 1}, "1": {"bowl": 1}}}, "multiagent_cooking_1_beetroot_soup_1_cooked_rabbit_blocked_access_0": {"conversation": "Let's collaborate to make beetroot_soup and cooked_rabbit.", "agent_count": 2, "target": {"beetroot_soup": 1, "cooked_rabbit": 1}, "type": "cooking", "timeout": 300, "recipes": {"beetroot_soup": ["Step 1: Go to the farm and collect 6 beetroot.", "Step 2: From your inventory or other agents get a bowl.", "Step 3: Go to the crafting table and combine the 6 beetroot and 1 bowl to make beetroot soup."], "cooked_rabbit": ["Step 1: Kill a rabbit and pick up 1 raw rabbit that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 2: Go to furnace and use it to cook the raw rabbit."]}, "blocked_access_to_recipe": ["0"], "goal": {"0": "Collaborate with agents around you to make 1 beetroot_soup, 1 cooked_rabbit. ", "1": "Collaborate with agents around you to make 1 beetroot_soup, 1 cooked_rabbit. Recipe for beetroot_soup:\n['Step 1: Go to the farm and collect 6 beetroot.', 'Step 2: From your inventory or other agents get a bowl.', 'Step 3: Go to the crafting table and combine the 6 beetroot and 1 bowl to make beetroot soup.']Recipe for cooked_rabbit:\n['Step 1: Kill a rabbit and pick up 1 raw rabbit that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 2: Go to furnace and use it to cook the raw rabbit.']"}, "difficulty_metrics": {"total_recipe_steps": 5, "blocked_agents_count": 1, "unique_target_items": 2, "overall_difficulty_score": 5, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"bowl": 1}, "1": {"coal": 1}}}, "multiagent_cooking_1_cooked_chicken_1_cooked_rabbit_blocked_access_0_1": {"conversation": "Let's collaborate to make cooked_rabbit and cooked_chicken.", "agent_count": 2, "target": {"cooked_rabbit": 1, "cooked_chicken": 1}, "type": "cooking", "timeout": 300, "recipes": {"cooked_rabbit": ["Step 1: Kill a rabbit and pick up 1 raw rabbit that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 2: Go to furnace and use it to cook the raw rabbit."], "cooked_chicken": ["Step 1: Kill a chicken and pick up 1 raw chicken that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the raw chicken."]}, "blocked_access_to_recipe": ["0", "1"], "goal": {"0": "Collaborate with agents around you to make 1 cooked_rabbit, 1 cooked_chicken. ", "1": "Collaborate with agents around you to make 1 cooked_rabbit, 1 cooked_chicken. "}, "difficulty_metrics": {"total_recipe_steps": 4, "blocked_agents_count": 2, "unique_target_items": 2, "overall_difficulty_score": 5, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"coal": 1}, "1": {"coal": 1}}}, "multiagent_cooking_1_cooked_mutton_1_cookie_blocked_access_1": {"conversation": "Let's collaborate to make cookie and cooked_mutton.", "agent_count": 2, "target": {"cookie": 1, "cooked_mutton": 1}, "type": "cooking", "timeout": 300, "recipes": {"cookie": ["Step 1: Go to the farm and collect 2 wheat.", "Step 2: Get 1 cocoa bean from your inventory or other agents.", "Step 3: Go to the crafting table and combine the wheat and cocoa bean to craft a cookie."], "cooked_mutton": ["Step 1: Kill a sheep and pick up 1 mutton that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the mutton."]}, "blocked_access_to_recipe": ["1"], "goal": {"0": "Collaborate with agents around you to make 1 cookie, 1 cooked_mutton. Recipe for cookie:\n['Step 1: Go to the farm and collect 2 wheat.', 'Step 2: Get 1 cocoa bean from your inventory or other agents.', 'Step 3: Go to the crafting table and combine the wheat and cocoa bean to craft a cookie.']Recipe for cooked_mutton:\n['Step 1: Kill a sheep and pick up 1 mutton that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the mutton.']", "1": "Collaborate with agents around you to make 1 cookie, 1 cooked_mutton. "}, "difficulty_metrics": {"total_recipe_steps": 5, "blocked_agents_count": 1, "unique_target_items": 2, "overall_difficulty_score": 5, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"cocoa_beans": 1}, "1": {"coal": 1}}}, "multiagent_cooking_1_beetroot_soup_1_mushroom_stew_blocked_access_0_1": {"conversation": "Let's collaborate to make mushroom_stew and beetroot_soup.", "agent_count": 2, "target": {"mushroom_stew": 1, "beetroot_soup": 1}, "type": "cooking", "timeout": 300, "recipes": {"mushroom_stew": ["Step 1: Go to the farm and collect 1 red mushroom and 1 brown mushroom.", "Step 2: From your inventory or other agents get a bowl.", "Step 3: Go to the crafting table and combine both the mushrooms and bowl to make mushroom stew."], "beetroot_soup": ["Step 1: Go to the farm and collect 6 beetroot.", "Step 2: From your inventory or other agents get a bowl.", "Step 3: Go to the crafting table and combine the 6 beetroot and 1 bowl to make beetroot soup."]}, "blocked_access_to_recipe": ["0", "1"], "goal": {"0": "Collaborate with agents around you to make 1 mushroom_stew, 1 beetroot_soup. ", "1": "Collaborate with agents around you to make 1 mushroom_stew, 1 beetroot_soup. "}, "difficulty_metrics": {"total_recipe_steps": 6, "blocked_agents_count": 2, "unique_target_items": 2, "overall_difficulty_score": 6, "difficulty_category": "hard"}, "difficulty": "hard", "initial_inventory": {"0": {"bowl": 1}, "1": {"bowl": 1}}}, "multiagent_cooking_1_beetroot_soup_1_suspicious_stew": {"conversation": "Let's collaborate to make suspicious_stew and beetroot_soup.", "agent_count": 2, "target": {"suspicious_stew": 1, "beetroot_soup": 1}, "type": "cooking", "timeout": 300, "recipes": {"suspicious_stew": ["Step 1: Go to the farm and collect 1 red mushroom, 1 brown mushroom.", "Step 2: From your inventory or other agents get a bowl and 1 dandelion", "Step 3: Go to the crafting table and combine the mushrooms, dandelion, and bowl to make suspicious stew."], "beetroot_soup": ["Step 1: Go to the farm and collect 6 beetroot.", "Step 2: From your inventory or other agents get a bowl.", "Step 3: Go to the crafting table and combine the 6 beetroot and 1 bowl to make beetroot soup."]}, "blocked_access_to_recipe": [], "goal": {"0": "Collaborate with agents around you to make 1 suspicious_stew, 1 beetroot_soup. Recipe for suspicious_stew:\n['Step 1: Go to the farm and collect 1 red mushroom, 1 brown mushroom.', 'Step 2: From your inventory or other agents get a bowl and 1 dandelion', 'Step 3: Go to the crafting table and combine the mushrooms, dandelion, and bowl to make suspicious stew.']Recipe for beetroot_soup:\n['Step 1: Go to the farm and collect 6 beetroot.', 'Step 2: From your inventory or other agents get a bowl.', 'Step 3: Go to the crafting table and combine the 6 beetroot and 1 bowl to make beetroot soup.']", "1": "Collaborate with agents around you to make 1 suspicious_stew, 1 beetroot_soup. Recipe for suspicious_stew:\n['Step 1: Go to the farm and collect 1 red mushroom, 1 brown mushroom.', 'Step 2: From your inventory or other agents get a bowl and 1 dandelion', 'Step 3: Go to the crafting table and combine the mushrooms, dandelion, and bowl to make suspicious stew.']Recipe for beetroot_soup:\n['Step 1: Go to the farm and collect 6 beetroot.', 'Step 2: From your inventory or other agents get a bowl.', 'Step 3: Go to the crafting table and combine the 6 beetroot and 1 bowl to make beetroot soup.']"}, "difficulty_metrics": {"total_recipe_steps": 6, "blocked_agents_count": 0, "unique_target_items": 2, "overall_difficulty_score": 4, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"bowl": 1, "dandelion": 1}, "1": {"bowl": 1}}}, "multiagent_cooking_1_beetroot_soup_1_cooked_porkchop_blocked_access_0_1": {"conversation": "Let's collaborate to make beetroot_soup and cooked_porkchop.", "agent_count": 2, "target": {"beetroot_soup": 1, "cooked_porkchop": 1}, "type": "cooking", "timeout": 300, "recipes": {"beetroot_soup": ["Step 1: Go to the farm and collect 6 beetroot.", "Step 2: From your inventory or other agents get a bowl.", "Step 3: Go to the crafting table and combine the 6 beetroot and 1 bowl to make beetroot soup."], "cooked_porkchop": ["Step 1: Kill a pig and pick up 1 porkchop that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the porkchop."]}, "blocked_access_to_recipe": ["0", "1"], "goal": {"0": "Collaborate with agents around you to make 1 beetroot_soup, 1 cooked_porkchop. ", "1": "Collaborate with agents around you to make 1 beetroot_soup, 1 cooked_porkchop. "}, "difficulty_metrics": {"total_recipe_steps": 5, "blocked_agents_count": 2, "unique_target_items": 2, "overall_difficulty_score": 6, "difficulty_category": "hard"}, "difficulty": "hard", "initial_inventory": {"0": {"bowl": 1}, "1": {"coal": 1}}}, "multiagent_cooking_1_beetroot_soup_1_pumpkin_pie_blocked_access_0": {"conversation": "Let's collaborate to make beetroot_soup and pumpkin_pie.", "agent_count": 2, "target": {"beetroot_soup": 1, "pumpkin_pie": 1}, "type": "cooking", "timeout": 300, "recipes": {"beetroot_soup": ["Step 1: Go to the farm and collect 6 beetroot.", "Step 2: From your inventory or other agents get a bowl.", "Step 3: Go to the crafting table and combine the 6 beetroot and 1 bowl to make beetroot soup."], "pumpkin_pie": ["Step 1: Go to the farm and collect 1 pumpkin and 1 sugar cane.", "Step 2: Get 1 egg from your inventory or other bots", "Step 3: Go to the crafting table and craft the sugar cane into sugar.", "Step 4: Go to the crafting table and combine the pumpkin, egg, and sugar to make a pumpkin pie."]}, "blocked_access_to_recipe": ["0"], "goal": {"0": "Collaborate with agents around you to make 1 beetroot_soup, 1 pumpkin_pie. ", "1": "Collaborate with agents around you to make 1 beetroot_soup, 1 pumpkin_pie. Recipe for beetroot_soup:\n['Step 1: Go to the farm and collect 6 beetroot.', 'Step 2: From your inventory or other agents get a bowl.', 'Step 3: Go to the crafting table and combine the 6 beetroot and 1 bowl to make beetroot soup.']Recipe for pumpkin_pie:\n['Step 1: Go to the farm and collect 1 pumpkin and 1 sugar cane.', 'Step 2: Get 1 egg from your inventory or other bots', 'Step 3: Go to the crafting table and craft the sugar cane into sugar.', 'Step 4: Go to the crafting table and combine the pumpkin, egg, and sugar to make a pumpkin pie.']"}, "difficulty_metrics": {"total_recipe_steps": 7, "blocked_agents_count": 1, "unique_target_items": 2, "overall_difficulty_score": 5, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"bowl": 1}, "1": {"egg": 1}}}, "multiagent_cooking_1_beetroot_soup_1_mushroom_stew": {"conversation": "Let's collaborate to make mushroom_stew and beetroot_soup.", "agent_count": 2, "target": {"mushroom_stew": 1, "beetroot_soup": 1}, "type": "cooking", "timeout": 300, "recipes": {"mushroom_stew": ["Step 1: Go to the farm and collect 1 red mushroom and 1 brown mushroom.", "Step 2: From your inventory or other agents get a bowl.", "Step 3: Go to the crafting table and combine both the mushrooms and bowl to make mushroom stew."], "beetroot_soup": ["Step 1: Go to the farm and collect 6 beetroot.", "Step 2: From your inventory or other agents get a bowl.", "Step 3: Go to the crafting table and combine the 6 beetroot and 1 bowl to make beetroot soup."]}, "blocked_access_to_recipe": [], "goal": {"0": "Collaborate with agents around you to make 1 mushroom_stew, 1 beetroot_soup. Recipe for mushroom_stew:\n['Step 1: Go to the farm and collect 1 red mushroom and 1 brown mushroom.', 'Step 2: From your inventory or other agents get a bowl.', 'Step 3: Go to the crafting table and combine both the mushrooms and bowl to make mushroom stew.']Recipe for beetroot_soup:\n['Step 1: Go to the farm and collect 6 beetroot.', 'Step 2: From your inventory or other agents get a bowl.', 'Step 3: Go to the crafting table and combine the 6 beetroot and 1 bowl to make beetroot soup.']", "1": "Collaborate with agents around you to make 1 mushroom_stew, 1 beetroot_soup. Recipe for mushroom_stew:\n['Step 1: Go to the farm and collect 1 red mushroom and 1 brown mushroom.', 'Step 2: From your inventory or other agents get a bowl.', 'Step 3: Go to the crafting table and combine both the mushrooms and bowl to make mushroom stew.']Recipe for beetroot_soup:\n['Step 1: Go to the farm and collect 6 beetroot.', 'Step 2: From your inventory or other agents get a bowl.', 'Step 3: Go to the crafting table and combine the 6 beetroot and 1 bowl to make beetroot soup.']"}, "difficulty_metrics": {"total_recipe_steps": 6, "blocked_agents_count": 0, "unique_target_items": 2, "overall_difficulty_score": 4, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"bowl": 1}, "1": {"bowl": 1}}}, "multiagent_cooking_1_cookie_1_suspicious_stew": {"conversation": "Let's collaborate to make cookie and suspicious_stew.", "agent_count": 2, "target": {"cookie": 1, "suspicious_stew": 1}, "type": "cooking", "timeout": 300, "recipes": {"cookie": ["Step 1: Go to the farm and collect 2 wheat.", "Step 2: Get 1 cocoa bean from your inventory or other agents.", "Step 3: Go to the crafting table and combine the wheat and cocoa bean to craft a cookie."], "suspicious_stew": ["Step 1: Go to the farm and collect 1 red mushroom, 1 brown mushroom.", "Step 2: From your inventory or other agents get a bowl and 1 dandelion", "Step 3: Go to the crafting table and combine the mushrooms, dandelion, and bowl to make suspicious stew."]}, "blocked_access_to_recipe": [], "goal": {"0": "Collaborate with agents around you to make 1 cookie, 1 suspicious_stew. Recipe for cookie:\n['Step 1: Go to the farm and collect 2 wheat.', 'Step 2: Get 1 cocoa bean from your inventory or other agents.', 'Step 3: Go to the crafting table and combine the wheat and cocoa bean to craft a cookie.']Recipe for suspicious_stew:\n['Step 1: Go to the farm and collect 1 red mushroom, 1 brown mushroom.', 'Step 2: From your inventory or other agents get a bowl and 1 dandelion', 'Step 3: Go to the crafting table and combine the mushrooms, dandelion, and bowl to make suspicious stew.']", "1": "Collaborate with agents around you to make 1 cookie, 1 suspicious_stew. Recipe for cookie:\n['Step 1: Go to the farm and collect 2 wheat.', 'Step 2: Get 1 cocoa bean from your inventory or other agents.', 'Step 3: Go to the crafting table and combine the wheat and cocoa bean to craft a cookie.']Recipe for suspicious_stew:\n['Step 1: Go to the farm and collect 1 red mushroom, 1 brown mushroom.', 'Step 2: From your inventory or other agents get a bowl and 1 dandelion', 'Step 3: Go to the crafting table and combine the mushrooms, dandelion, and bowl to make suspicious stew.']"}, "difficulty_metrics": {"total_recipe_steps": 6, "blocked_agents_count": 0, "unique_target_items": 2, "overall_difficulty_score": 4, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"cocoa_beans": 1, "dandelion": 1}, "1": {"bowl": 1}}}, "multiagent_cooking_1_beetroot_soup_1_cookie": {"conversation": "Let's collaborate to make beetroot_soup and cookie.", "agent_count": 2, "target": {"beetroot_soup": 1, "cookie": 1}, "type": "cooking", "timeout": 300, "recipes": {"beetroot_soup": ["Step 1: Go to the farm and collect 6 beetroot.", "Step 2: From your inventory or other agents get a bowl.", "Step 3: Go to the crafting table and combine the 6 beetroot and 1 bowl to make beetroot soup."], "cookie": ["Step 1: Go to the farm and collect 2 wheat.", "Step 2: Get 1 cocoa bean from your inventory or other agents.", "Step 3: Go to the crafting table and combine the wheat and cocoa bean to craft a cookie."]}, "blocked_access_to_recipe": [], "goal": {"0": "Collaborate with agents around you to make 1 beetroot_soup, 1 cookie. Recipe for beetroot_soup:\n['Step 1: Go to the farm and collect 6 beetroot.', 'Step 2: From your inventory or other agents get a bowl.', 'Step 3: Go to the crafting table and combine the 6 beetroot and 1 bowl to make beetroot soup.']Recipe for cookie:\n['Step 1: Go to the farm and collect 2 wheat.', 'Step 2: Get 1 cocoa bean from your inventory or other agents.', 'Step 3: Go to the crafting table and combine the wheat and cocoa bean to craft a cookie.']", "1": "Collaborate with agents around you to make 1 beetroot_soup, 1 cookie. Recipe for beetroot_soup:\n['Step 1: Go to the farm and collect 6 beetroot.', 'Step 2: From your inventory or other agents get a bowl.', 'Step 3: Go to the crafting table and combine the 6 beetroot and 1 bowl to make beetroot soup.']Recipe for cookie:\n['Step 1: Go to the farm and collect 2 wheat.', 'Step 2: Get 1 cocoa bean from your inventory or other agents.', 'Step 3: Go to the crafting table and combine the wheat and cocoa bean to craft a cookie.']"}, "difficulty_metrics": {"total_recipe_steps": 6, "blocked_agents_count": 0, "unique_target_items": 2, "overall_difficulty_score": 4, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"bowl": 1}, "1": {"cocoa_beans": 1}}}, "multiagent_cooking_1_golden_carrot_1_pumpkin_pie_blocked_access_0": {"conversation": "Let's collaborate to make pumpkin_pie and golden_carrot.", "agent_count": 2, "target": {"pumpkin_pie": 1, "golden_carrot": 1}, "type": "cooking", "timeout": 300, "recipes": {"pumpkin_pie": ["Step 1: Go to the farm and collect 1 pumpkin and 1 sugar cane.", "Step 2: Get 1 egg from your inventory or other bots", "Step 3: Go to the crafting table and craft the sugar cane into sugar.", "Step 4: Go to the crafting table and combine the pumpkin, egg, and sugar to make a pumpkin pie."], "golden_carrot": ["Step 1: Go to the farm and collect 1 carrot.", "Step 2: Go to the chest and collect gold ingots and convert them to gold nuggets.", "Step 3: Go to the crafting table and surround the carrot with gold nuggets to create a golden carrot."]}, "blocked_access_to_recipe": ["0"], "goal": {"0": "Collaborate with agents around you to make 1 pumpkin_pie, 1 golden_carrot. ", "1": "Collaborate with agents around you to make 1 pumpkin_pie, 1 golden_carrot. Recipe for pumpkin_pie:\n['Step 1: Go to the farm and collect 1 pumpkin and 1 sugar cane.', 'Step 2: Get 1 egg from your inventory or other bots', 'Step 3: Go to the crafting table and craft the sugar cane into sugar.', 'Step 4: Go to the crafting table and combine the pumpkin, egg, and sugar to make a pumpkin pie.']Recipe for golden_carrot:\n['Step 1: Go to the farm and collect 1 carrot.', 'Step 2: Go to the chest and collect gold ingots and convert them to gold nuggets.', 'Step 3: Go to the crafting table and surround the carrot with gold nuggets to create a golden carrot.']"}, "difficulty_metrics": {"total_recipe_steps": 7, "blocked_agents_count": 1, "unique_target_items": 2, "overall_difficulty_score": 5, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"egg": 1, "gold_ingot": 4}, "1": {"gold_ingot": 4}}}, "multiagent_cooking_1_golden_carrot_1_suspicious_stew_blocked_access_0_1": {"conversation": "Let's collaborate to make suspicious_stew and golden_carrot.", "agent_count": 2, "target": {"suspicious_stew": 1, "golden_carrot": 1}, "type": "cooking", "timeout": 300, "recipes": {"suspicious_stew": ["Step 1: Go to the farm and collect 1 red mushroom, 1 brown mushroom.", "Step 2: From your inventory or other agents get a bowl and 1 dandelion", "Step 3: Go to the crafting table and combine the mushrooms, dandelion, and bowl to make suspicious stew."], "golden_carrot": ["Step 1: Go to the farm and collect 1 carrot.", "Step 2: Go to the chest and collect gold ingots and convert them to gold nuggets.", "Step 3: Go to the crafting table and surround the carrot with gold nuggets to create a golden carrot."]}, "blocked_access_to_recipe": ["0", "1"], "goal": {"0": "Collaborate with agents around you to make 1 suspicious_stew, 1 golden_carrot. ", "1": "Collaborate with agents around you to make 1 suspicious_stew, 1 golden_carrot. "}, "difficulty_metrics": {"total_recipe_steps": 6, "blocked_agents_count": 2, "unique_target_items": 2, "overall_difficulty_score": 6, "difficulty_category": "hard"}, "difficulty": "hard", "initial_inventory": {"0": {"bowl": 1, "gold_ingot": 4}, "1": {"dandelion": 1, "gold_ingot": 4}}}, "multiagent_cooking_1_cooked_porkchop_1_cooked_rabbit_blocked_access_0_1": {"conversation": "Let's collaborate to make cooked_porkchop and cooked_rabbit.", "agent_count": 2, "target": {"cooked_porkchop": 1, "cooked_rabbit": 1}, "type": "cooking", "timeout": 300, "recipes": {"cooked_porkchop": ["Step 1: Kill a pig and pick up 1 porkchop that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the porkchop."], "cooked_rabbit": ["Step 1: Kill a rabbit and pick up 1 raw rabbit that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 2: Go to furnace and use it to cook the raw rabbit."]}, "blocked_access_to_recipe": ["0", "1"], "goal": {"0": "Collaborate with agents around you to make 1 cooked_porkchop, 1 cooked_rabbit. ", "1": "Collaborate with agents around you to make 1 cooked_porkchop, 1 cooked_rabbit. "}, "difficulty_metrics": {"total_recipe_steps": 4, "blocked_agents_count": 2, "unique_target_items": 2, "overall_difficulty_score": 5, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"coal": 1}, "1": {"coal": 1}}}, "multiagent_cooking_1_cooked_porkchop_1_cookie_blocked_access_0": {"conversation": "Let's collaborate to make cookie and cooked_porkchop.", "agent_count": 2, "target": {"cookie": 1, "cooked_porkchop": 1}, "type": "cooking", "timeout": 300, "recipes": {"cookie": ["Step 1: Go to the farm and collect 2 wheat.", "Step 2: Get 1 cocoa bean from your inventory or other agents.", "Step 3: Go to the crafting table and combine the wheat and cocoa bean to craft a cookie."], "cooked_porkchop": ["Step 1: Kill a pig and pick up 1 porkchop that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the porkchop."]}, "blocked_access_to_recipe": ["0"], "goal": {"0": "Collaborate with agents around you to make 1 cookie, 1 cooked_porkchop. ", "1": "Collaborate with agents around you to make 1 cookie, 1 cooked_porkchop. Recipe for cookie:\n['Step 1: Go to the farm and collect 2 wheat.', 'Step 2: Get 1 cocoa bean from your inventory or other agents.', 'Step 3: Go to the crafting table and combine the wheat and cocoa bean to craft a cookie.']Recipe for cooked_porkchop:\n['Step 1: Kill a pig and pick up 1 porkchop that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the porkchop.']"}, "difficulty_metrics": {"total_recipe_steps": 5, "blocked_agents_count": 1, "unique_target_items": 2, "overall_difficulty_score": 5, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"cocoa_beans": 1}, "1": {"coal": 1}}}, "multiagent_cooking_1_cooked_chicken_1_mushroom_stew": {"conversation": "Let's collaborate to make mushroom_stew and cooked_chicken.", "agent_count": 2, "target": {"mushroom_stew": 1, "cooked_chicken": 1}, "type": "cooking", "timeout": 300, "recipes": {"mushroom_stew": ["Step 1: Go to the farm and collect 1 red mushroom and 1 brown mushroom.", "Step 2: From your inventory or other agents get a bowl.", "Step 3: Go to the crafting table and combine both the mushrooms and bowl to make mushroom stew."], "cooked_chicken": ["Step 1: Kill a chicken and pick up 1 raw chicken that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the raw chicken."]}, "blocked_access_to_recipe": [], "goal": {"0": "Collaborate with agents around you to make 1 mushroom_stew, 1 cooked_chicken. Recipe for mushroom_stew:\n['Step 1: Go to the farm and collect 1 red mushroom and 1 brown mushroom.', 'Step 2: From your inventory or other agents get a bowl.', 'Step 3: Go to the crafting table and combine both the mushrooms and bowl to make mushroom stew.']Recipe for cooked_chicken:\n['Step 1: Kill a chicken and pick up 1 raw chicken that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the raw chicken.']", "1": "Collaborate with agents around you to make 1 mushroom_stew, 1 cooked_chicken. Recipe for mushroom_stew:\n['Step 1: Go to the farm and collect 1 red mushroom and 1 brown mushroom.', 'Step 2: From your inventory or other agents get a bowl.', 'Step 3: Go to the crafting table and combine both the mushrooms and bowl to make mushroom stew.']Recipe for cooked_chicken:\n['Step 1: Kill a chicken and pick up 1 raw chicken that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the raw chicken.']"}, "difficulty_metrics": {"total_recipe_steps": 5, "blocked_agents_count": 0, "unique_target_items": 2, "overall_difficulty_score": 4, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"bowl": 1}, "1": {"coal": 1}}}, "multiagent_cooking_1_cooked_porkchop_1_cooked_rabbit": {"conversation": "Let's collaborate to make cooked_rabbit and cooked_porkchop.", "agent_count": 2, "target": {"cooked_rabbit": 1, "cooked_porkchop": 1}, "type": "cooking", "timeout": 300, "recipes": {"cooked_rabbit": ["Step 1: Kill a rabbit and pick up 1 raw rabbit that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 2: Go to furnace and use it to cook the raw rabbit."], "cooked_porkchop": ["Step 1: Kill a pig and pick up 1 porkchop that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the porkchop."]}, "blocked_access_to_recipe": [], "goal": {"0": "Collaborate with agents around you to make 1 cooked_rabbit, 1 cooked_porkchop. Recipe for cooked_rabbit:\n['Step 1: Kill a rabbit and pick up 1 raw rabbit that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 2: Go to furnace and use it to cook the raw rabbit.']Recipe for cooked_porkchop:\n['Step 1: Kill a pig and pick up 1 porkchop that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the porkchop.']", "1": "Collaborate with agents around you to make 1 cooked_rabbit, 1 cooked_porkchop. Recipe for cooked_rabbit:\n['Step 1: Kill a rabbit and pick up 1 raw rabbit that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 2: Go to furnace and use it to cook the raw rabbit.']Recipe for cooked_porkchop:\n['Step 1: Kill a pig and pick up 1 porkchop that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the porkchop.']"}, "difficulty_metrics": {"total_recipe_steps": 4, "blocked_agents_count": 0, "unique_target_items": 2, "overall_difficulty_score": 3, "difficulty_category": "easy"}, "difficulty": "easy", "initial_inventory": {"0": {"coal": 1}, "1": {"coal": 1}}}, "multiagent_cooking_1_cooked_chicken_1_mushroom_stew_blocked_access_1": {"conversation": "Let's collaborate to make cooked_chicken and mushroom_stew.", "agent_count": 2, "target": {"cooked_chicken": 1, "mushroom_stew": 1}, "type": "cooking", "timeout": 300, "recipes": {"cooked_chicken": ["Step 1: Kill a chicken and pick up 1 raw chicken that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the raw chicken."], "mushroom_stew": ["Step 1: Go to the farm and collect 1 red mushroom and 1 brown mushroom.", "Step 2: From your inventory or other agents get a bowl.", "Step 3: Go to the crafting table and combine both the mushrooms and bowl to make mushroom stew."]}, "blocked_access_to_recipe": ["1"], "goal": {"0": "Collaborate with agents around you to make 1 cooked_chicken, 1 mushroom_stew. Recipe for cooked_chicken:\n['Step 1: Kill a chicken and pick up 1 raw chicken that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the raw chicken.']Recipe for mushroom_stew:\n['Step 1: Go to the farm and collect 1 red mushroom and 1 brown mushroom.', 'Step 2: From your inventory or other agents get a bowl.', 'Step 3: Go to the crafting table and combine both the mushrooms and bowl to make mushroom stew.']", "1": "Collaborate with agents around you to make 1 cooked_chicken, 1 mushroom_stew. "}, "difficulty_metrics": {"total_recipe_steps": 5, "blocked_agents_count": 1, "unique_target_items": 2, "overall_difficulty_score": 5, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"coal": 1}, "1": {"bowl": 1}}}, "multiagent_cooking_1_suspicious_stew_blocked_access_1": {"conversation": "Let's work together to make suspicious_stew.", "agent_count": 2, "target": {"suspicious_stew": 1}, "type": "cooking", "timeout": 300, "recipes": {"suspicious_stew": ["Step 1: Go to the farm and collect 1 red mushroom, 1 brown mushroom.", "Step 2: From your inventory or other agents get a bowl and 1 dandelion", "Step 3: Go to the crafting table and combine the mushrooms, dandelion, and bowl to make suspicious stew."]}, "blocked_access_to_recipe": ["1"], "goal": {"0": "Collaborate with agents around you to make 1 suspicious_stew. Recipe for suspicious_stew:\n['Step 1: Go to the farm and collect 1 red mushroom, 1 brown mushroom.', 'Step 2: From your inventory or other agents get a bowl and 1 dandelion', 'Step 3: Go to the crafting table and combine the mushrooms, dandelion, and bowl to make suspicious stew.']", "1": "Collaborate with agents around you to make 1 suspicious_stew. "}, "difficulty_metrics": {"total_recipe_steps": 3, "blocked_agents_count": 1, "unique_target_items": 1, "overall_difficulty_score": 3, "difficulty_category": "easy"}, "difficulty": "easy", "initial_inventory": {"0": {"bowl": 1}, "1": {"dandelion": 1}}}, "multiagent_cooking_1_cooked_porkchop_1_suspicious_stew_blocked_access_0": {"conversation": "Let's collaborate to make suspicious_stew and cooked_porkchop.", "agent_count": 2, "target": {"suspicious_stew": 1, "cooked_porkchop": 1}, "type": "cooking", "timeout": 300, "recipes": {"suspicious_stew": ["Step 1: Go to the farm and collect 1 red mushroom, 1 brown mushroom.", "Step 2: From your inventory or other agents get a bowl and 1 dandelion", "Step 3: Go to the crafting table and combine the mushrooms, dandelion, and bowl to make suspicious stew."], "cooked_porkchop": ["Step 1: Kill a pig and pick up 1 porkchop that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the porkchop."]}, "blocked_access_to_recipe": ["0"], "goal": {"0": "Collaborate with agents around you to make 1 suspicious_stew, 1 cooked_porkchop. ", "1": "Collaborate with agents around you to make 1 suspicious_stew, 1 cooked_porkchop. Recipe for suspicious_stew:\n['Step 1: Go to the farm and collect 1 red mushroom, 1 brown mushroom.', 'Step 2: From your inventory or other agents get a bowl and 1 dandelion', 'Step 3: Go to the crafting table and combine the mushrooms, dandelion, and bowl to make suspicious stew.']Recipe for cooked_porkchop:\n['Step 1: Kill a pig and pick up 1 porkchop that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the porkchop.']"}, "difficulty_metrics": {"total_recipe_steps": 5, "blocked_agents_count": 1, "unique_target_items": 2, "overall_difficulty_score": 5, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"bowl": 1, "coal": 1}, "1": {"dandelion": 1}}}, "multiagent_cooking_1_cooked_rabbit_1_mushroom_stew_blocked_access_0_1": {"conversation": "Let's collaborate to make cooked_rabbit and mushroom_stew.", "agent_count": 2, "target": {"cooked_rabbit": 1, "mushroom_stew": 1}, "type": "cooking", "timeout": 300, "recipes": {"cooked_rabbit": ["Step 1: Kill a rabbit and pick up 1 raw rabbit that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 2: Go to furnace and use it to cook the raw rabbit."], "mushroom_stew": ["Step 1: Go to the farm and collect 1 red mushroom and 1 brown mushroom.", "Step 2: From your inventory or other agents get a bowl.", "Step 3: Go to the crafting table and combine both the mushrooms and bowl to make mushroom stew."]}, "blocked_access_to_recipe": ["0", "1"], "goal": {"0": "Collaborate with agents around you to make 1 cooked_rabbit, 1 mushroom_stew. ", "1": "Collaborate with agents around you to make 1 cooked_rabbit, 1 mushroom_stew. "}, "difficulty_metrics": {"total_recipe_steps": 5, "blocked_agents_count": 2, "unique_target_items": 2, "overall_difficulty_score": 6, "difficulty_category": "hard"}, "difficulty": "hard", "initial_inventory": {"0": {"coal": 1}, "1": {"bowl": 1}}}, "multiagent_cooking_1_cooked_rabbit_1_cookie": {"conversation": "Let's collaborate to make cooked_rabbit and cookie.", "agent_count": 2, "target": {"cooked_rabbit": 1, "cookie": 1}, "type": "cooking", "timeout": 300, "recipes": {"cooked_rabbit": ["Step 1: Kill a rabbit and pick up 1 raw rabbit that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 2: Go to furnace and use it to cook the raw rabbit."], "cookie": ["Step 1: Go to the farm and collect 2 wheat.", "Step 2: Get 1 cocoa bean from your inventory or other agents.", "Step 3: Go to the crafting table and combine the wheat and cocoa bean to craft a cookie."]}, "blocked_access_to_recipe": [], "goal": {"0": "Collaborate with agents around you to make 1 cooked_rabbit, 1 cookie. Recipe for cooked_rabbit:\n['Step 1: Kill a rabbit and pick up 1 raw rabbit that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 2: Go to furnace and use it to cook the raw rabbit.']Recipe for cookie:\n['Step 1: Go to the farm and collect 2 wheat.', 'Step 2: Get 1 cocoa bean from your inventory or other agents.', 'Step 3: Go to the crafting table and combine the wheat and cocoa bean to craft a cookie.']", "1": "Collaborate with agents around you to make 1 cooked_rabbit, 1 cookie. Recipe for cooked_rabbit:\n['Step 1: Kill a rabbit and pick up 1 raw rabbit that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 2: Go to furnace and use it to cook the raw rabbit.']Recipe for cookie:\n['Step 1: Go to the farm and collect 2 wheat.', 'Step 2: Get 1 cocoa bean from your inventory or other agents.', 'Step 3: Go to the crafting table and combine the wheat and cocoa bean to craft a cookie.']"}, "difficulty_metrics": {"total_recipe_steps": 5, "blocked_agents_count": 0, "unique_target_items": 2, "overall_difficulty_score": 4, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"coal": 1}, "1": {"cocoa_beans": 1}}}, "multiagent_cooking_1_cooked_mutton_1_mushroom_stew": {"conversation": "Let's collaborate to make mushroom_stew and cooked_mutton.", "agent_count": 2, "target": {"mushroom_stew": 1, "cooked_mutton": 1}, "type": "cooking", "timeout": 300, "recipes": {"mushroom_stew": ["Step 1: Go to the farm and collect 1 red mushroom and 1 brown mushroom.", "Step 2: From your inventory or other agents get a bowl.", "Step 3: Go to the crafting table and combine both the mushrooms and bowl to make mushroom stew."], "cooked_mutton": ["Step 1: Kill a sheep and pick up 1 mutton that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the mutton."]}, "blocked_access_to_recipe": [], "goal": {"0": "Collaborate with agents around you to make 1 mushroom_stew, 1 cooked_mutton. Recipe for mushroom_stew:\n['Step 1: Go to the farm and collect 1 red mushroom and 1 brown mushroom.', 'Step 2: From your inventory or other agents get a bowl.', 'Step 3: Go to the crafting table and combine both the mushrooms and bowl to make mushroom stew.']Recipe for cooked_mutton:\n['Step 1: Kill a sheep and pick up 1 mutton that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the mutton.']", "1": "Collaborate with agents around you to make 1 mushroom_stew, 1 cooked_mutton. Recipe for mushroom_stew:\n['Step 1: Go to the farm and collect 1 red mushroom and 1 brown mushroom.', 'Step 2: From your inventory or other agents get a bowl.', 'Step 3: Go to the crafting table and combine both the mushrooms and bowl to make mushroom stew.']Recipe for cooked_mutton:\n['Step 1: Kill a sheep and pick up 1 mutton that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the mutton.']"}, "difficulty_metrics": {"total_recipe_steps": 5, "blocked_agents_count": 0, "unique_target_items": 2, "overall_difficulty_score": 4, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"bowl": 1}, "1": {"coal": 1}}}, "multiagent_cooking_1_beetroot_soup_1_cooked_porkchop": {"conversation": "Let's collaborate to make beetroot_soup and cooked_porkchop.", "agent_count": 2, "target": {"beetroot_soup": 1, "cooked_porkchop": 1}, "type": "cooking", "timeout": 300, "recipes": {"beetroot_soup": ["Step 1: Go to the farm and collect 6 beetroot.", "Step 2: From your inventory or other agents get a bowl.", "Step 3: Go to the crafting table and combine the 6 beetroot and 1 bowl to make beetroot soup."], "cooked_porkchop": ["Step 1: Kill a pig and pick up 1 porkchop that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the porkchop."]}, "blocked_access_to_recipe": [], "goal": {"0": "Collaborate with agents around you to make 1 beetroot_soup, 1 cooked_porkchop. Recipe for beetroot_soup:\n['Step 1: Go to the farm and collect 6 beetroot.', 'Step 2: From your inventory or other agents get a bowl.', 'Step 3: Go to the crafting table and combine the 6 beetroot and 1 bowl to make beetroot soup.']Recipe for cooked_porkchop:\n['Step 1: Kill a pig and pick up 1 porkchop that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the porkchop.']", "1": "Collaborate with agents around you to make 1 beetroot_soup, 1 cooked_porkchop. Recipe for beetroot_soup:\n['Step 1: Go to the farm and collect 6 beetroot.', 'Step 2: From your inventory or other agents get a bowl.', 'Step 3: Go to the crafting table and combine the 6 beetroot and 1 bowl to make beetroot soup.']Recipe for cooked_porkchop:\n['Step 1: Kill a pig and pick up 1 porkchop that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the porkchop.']"}, "difficulty_metrics": {"total_recipe_steps": 5, "blocked_agents_count": 0, "unique_target_items": 2, "overall_difficulty_score": 4, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"bowl": 1}, "1": {"coal": 1}}}, "multiagent_cooking_1_cooked_chicken_1_cooked_rabbit_blocked_access_1": {"conversation": "Let's collaborate to make cooked_chicken and cooked_rabbit.", "agent_count": 2, "target": {"cooked_chicken": 1, "cooked_rabbit": 1}, "type": "cooking", "timeout": 300, "recipes": {"cooked_chicken": ["Step 1: Kill a chicken and pick up 1 raw chicken that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the raw chicken."], "cooked_rabbit": ["Step 1: Kill a rabbit and pick up 1 raw rabbit that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 2: Go to furnace and use it to cook the raw rabbit."]}, "blocked_access_to_recipe": ["1"], "goal": {"0": "Collaborate with agents around you to make 1 cooked_chicken, 1 cooked_rabbit. Recipe for cooked_chicken:\n['Step 1: Kill a chicken and pick up 1 raw chicken that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the raw chicken.']Recipe for cooked_rabbit:\n['Step 1: Kill a rabbit and pick up 1 raw rabbit that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 2: Go to furnace and use it to cook the raw rabbit.']", "1": "Collaborate with agents around you to make 1 cooked_chicken, 1 cooked_rabbit. "}, "difficulty_metrics": {"total_recipe_steps": 4, "blocked_agents_count": 1, "unique_target_items": 2, "overall_difficulty_score": 4, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"coal": 1}, "1": {"coal": 1}}}, "multiagent_cooking_1_cooked_chicken_1_cooked_porkchop": {"conversation": "Let's collaborate to make cooked_porkchop and cooked_chicken.", "agent_count": 2, "target": {"cooked_porkchop": 1, "cooked_chicken": 1}, "type": "cooking", "timeout": 300, "recipes": {"cooked_porkchop": ["Step 1: Kill a pig and pick up 1 porkchop that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the porkchop."], "cooked_chicken": ["Step 1: Kill a chicken and pick up 1 raw chicken that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the raw chicken."]}, "blocked_access_to_recipe": [], "goal": {"0": "Collaborate with agents around you to make 1 cooked_porkchop, 1 cooked_chicken. Recipe for cooked_porkchop:\n['Step 1: Kill a pig and pick up 1 porkchop that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the porkchop.']Recipe for cooked_chicken:\n['Step 1: Kill a chicken and pick up 1 raw chicken that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the raw chicken.']", "1": "Collaborate with agents around you to make 1 cooked_porkchop, 1 cooked_chicken. Recipe for cooked_porkchop:\n['Step 1: Kill a pig and pick up 1 porkchop that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the porkchop.']Recipe for cooked_chicken:\n['Step 1: Kill a chicken and pick up 1 raw chicken that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the raw chicken.']"}, "difficulty_metrics": {"total_recipe_steps": 4, "blocked_agents_count": 0, "unique_target_items": 2, "overall_difficulty_score": 3, "difficulty_category": "easy"}, "difficulty": "easy", "initial_inventory": {"0": {"coal": 1}, "1": {"coal": 1}}}, "multiagent_cooking_1_suspicious_stew": {"conversation": "Let's work together to make suspicious_stew.", "agent_count": 2, "target": {"suspicious_stew": 1}, "type": "cooking", "timeout": 300, "recipes": {"suspicious_stew": ["Step 1: Go to the farm and collect 1 red mushroom, 1 brown mushroom.", "Step 2: From your inventory or other agents get a bowl and 1 dandelion", "Step 3: Go to the crafting table and combine the mushrooms, dandelion, and bowl to make suspicious stew."]}, "blocked_access_to_recipe": [], "goal": {"0": "Collaborate with agents around you to make 1 suspicious_stew. Recipe for suspicious_stew:\n['Step 1: Go to the farm and collect 1 red mushroom, 1 brown mushroom.', 'Step 2: From your inventory or other agents get a bowl and 1 dandelion', 'Step 3: Go to the crafting table and combine the mushrooms, dandelion, and bowl to make suspicious stew.']", "1": "Collaborate with agents around you to make 1 suspicious_stew. Recipe for suspicious_stew:\n['Step 1: Go to the farm and collect 1 red mushroom, 1 brown mushroom.', 'Step 2: From your inventory or other agents get a bowl and 1 dandelion', 'Step 3: Go to the crafting table and combine the mushrooms, dandelion, and bowl to make suspicious stew.']"}, "difficulty_metrics": {"total_recipe_steps": 3, "blocked_agents_count": 0, "unique_target_items": 1, "overall_difficulty_score": 2, "difficulty_category": "easy"}, "difficulty": "easy", "initial_inventory": {"0": {"bowl": 1}, "1": {"dandelion": 1}}}, "multiagent_cooking_1_pumpkin_pie_1_suspicious_stew": {"conversation": "Let's collaborate to make pumpkin_pie and suspicious_stew.", "agent_count": 2, "target": {"pumpkin_pie": 1, "suspicious_stew": 1}, "type": "cooking", "timeout": 300, "recipes": {"pumpkin_pie": ["Step 1: Go to the farm and collect 1 pumpkin and 1 sugar cane.", "Step 2: Get 1 egg from your inventory or other bots", "Step 3: Go to the crafting table and craft the sugar cane into sugar.", "Step 4: Go to the crafting table and combine the pumpkin, egg, and sugar to make a pumpkin pie."], "suspicious_stew": ["Step 1: Go to the farm and collect 1 red mushroom, 1 brown mushroom.", "Step 2: From your inventory or other agents get a bowl and 1 dandelion", "Step 3: Go to the crafting table and combine the mushrooms, dandelion, and bowl to make suspicious stew."]}, "blocked_access_to_recipe": [], "goal": {"0": "Collaborate with agents around you to make 1 pumpkin_pie, 1 suspicious_stew. Recipe for pumpkin_pie:\n['Step 1: Go to the farm and collect 1 pumpkin and 1 sugar cane.', 'Step 2: Get 1 egg from your inventory or other bots', 'Step 3: Go to the crafting table and craft the sugar cane into sugar.', 'Step 4: Go to the crafting table and combine the pumpkin, egg, and sugar to make a pumpkin pie.']Recipe for suspicious_stew:\n['Step 1: Go to the farm and collect 1 red mushroom, 1 brown mushroom.', 'Step 2: From your inventory or other agents get a bowl and 1 dandelion', 'Step 3: Go to the crafting table and combine the mushrooms, dandelion, and bowl to make suspicious stew.']", "1": "Collaborate with agents around you to make 1 pumpkin_pie, 1 suspicious_stew. Recipe for pumpkin_pie:\n['Step 1: Go to the farm and collect 1 pumpkin and 1 sugar cane.', 'Step 2: Get 1 egg from your inventory or other bots', 'Step 3: Go to the crafting table and craft the sugar cane into sugar.', 'Step 4: Go to the crafting table and combine the pumpkin, egg, and sugar to make a pumpkin pie.']Recipe for suspicious_stew:\n['Step 1: Go to the farm and collect 1 red mushroom, 1 brown mushroom.', 'Step 2: From your inventory or other agents get a bowl and 1 dandelion', 'Step 3: Go to the crafting table and combine the mushrooms, dandelion, and bowl to make suspicious stew.']"}, "difficulty_metrics": {"total_recipe_steps": 7, "blocked_agents_count": 0, "unique_target_items": 2, "overall_difficulty_score": 4, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"egg": 1, "dandelion": 1}, "1": {"bowl": 1}}}, "multiagent_cooking_1_cooked_chicken_1_mushroom_stew_blocked_access_0_1": {"conversation": "Let's collaborate to make mushroom_stew and cooked_chicken.", "agent_count": 2, "target": {"mushroom_stew": 1, "cooked_chicken": 1}, "type": "cooking", "timeout": 300, "recipes": {"mushroom_stew": ["Step 1: Go to the farm and collect 1 red mushroom and 1 brown mushroom.", "Step 2: From your inventory or other agents get a bowl.", "Step 3: Go to the crafting table and combine both the mushrooms and bowl to make mushroom stew."], "cooked_chicken": ["Step 1: Kill a chicken and pick up 1 raw chicken that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the raw chicken."]}, "blocked_access_to_recipe": ["0", "1"], "goal": {"0": "Collaborate with agents around you to make 1 mushroom_stew, 1 cooked_chicken. ", "1": "Collaborate with agents around you to make 1 mushroom_stew, 1 cooked_chicken. "}, "difficulty_metrics": {"total_recipe_steps": 5, "blocked_agents_count": 2, "unique_target_items": 2, "overall_difficulty_score": 6, "difficulty_category": "hard"}, "difficulty": "hard", "initial_inventory": {"0": {"bowl": 1}, "1": {"coal": 1}}}, "multiagent_cooking_1_cookie_1_golden_carrot": {"conversation": "Let's collaborate to make cookie and golden_carrot.", "agent_count": 2, "target": {"cookie": 1, "golden_carrot": 1}, "type": "cooking", "timeout": 300, "recipes": {"cookie": ["Step 1: Go to the farm and collect 2 wheat.", "Step 2: Get 1 cocoa bean from your inventory or other agents.", "Step 3: Go to the crafting table and combine the wheat and cocoa bean to craft a cookie."], "golden_carrot": ["Step 1: Go to the farm and collect 1 carrot.", "Step 2: Go to the chest and collect gold ingots and convert them to gold nuggets.", "Step 3: Go to the crafting table and surround the carrot with gold nuggets to create a golden carrot."]}, "blocked_access_to_recipe": [], "goal": {"0": "Collaborate with agents around you to make 1 cookie, 1 golden_carrot. Recipe for cookie:\n['Step 1: Go to the farm and collect 2 wheat.', 'Step 2: Get 1 cocoa bean from your inventory or other agents.', 'Step 3: Go to the crafting table and combine the wheat and cocoa bean to craft a cookie.']Recipe for golden_carrot:\n['Step 1: Go to the farm and collect 1 carrot.', 'Step 2: Go to the chest and collect gold ingots and convert them to gold nuggets.', 'Step 3: Go to the crafting table and surround the carrot with gold nuggets to create a golden carrot.']", "1": "Collaborate with agents around you to make 1 cookie, 1 golden_carrot. Recipe for cookie:\n['Step 1: Go to the farm and collect 2 wheat.', 'Step 2: Get 1 cocoa bean from your inventory or other agents.', 'Step 3: Go to the crafting table and combine the wheat and cocoa bean to craft a cookie.']Recipe for golden_carrot:\n['Step 1: Go to the farm and collect 1 carrot.', 'Step 2: Go to the chest and collect gold ingots and convert them to gold nuggets.', 'Step 3: Go to the crafting table and surround the carrot with gold nuggets to create a golden carrot.']"}, "difficulty_metrics": {"total_recipe_steps": 6, "blocked_agents_count": 0, "unique_target_items": 2, "overall_difficulty_score": 4, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"cocoa_beans": 1, "gold_ingot": 4}, "1": {"gold_ingot": 4}}}, "multiagent_cooking_1_beetroot_soup_1_golden_carrot": {"conversation": "Let's collaborate to make golden_carrot and beetroot_soup.", "agent_count": 2, "target": {"golden_carrot": 1, "beetroot_soup": 1}, "type": "cooking", "timeout": 300, "recipes": {"golden_carrot": ["Step 1: Go to the farm and collect 1 carrot.", "Step 2: Go to the chest and collect gold ingots and convert them to gold nuggets.", "Step 3: Go to the crafting table and surround the carrot with gold nuggets to create a golden carrot."], "beetroot_soup": ["Step 1: Go to the farm and collect 6 beetroot.", "Step 2: From your inventory or other agents get a bowl.", "Step 3: Go to the crafting table and combine the 6 beetroot and 1 bowl to make beetroot soup."]}, "blocked_access_to_recipe": [], "goal": {"0": "Collaborate with agents around you to make 1 golden_carrot, 1 beetroot_soup. Recipe for golden_carrot:\n['Step 1: Go to the farm and collect 1 carrot.', 'Step 2: Go to the chest and collect gold ingots and convert them to gold nuggets.', 'Step 3: Go to the crafting table and surround the carrot with gold nuggets to create a golden carrot.']Recipe for beetroot_soup:\n['Step 1: Go to the farm and collect 6 beetroot.', 'Step 2: From your inventory or other agents get a bowl.', 'Step 3: Go to the crafting table and combine the 6 beetroot and 1 bowl to make beetroot soup.']", "1": "Collaborate with agents around you to make 1 golden_carrot, 1 beetroot_soup. Recipe for golden_carrot:\n['Step 1: Go to the farm and collect 1 carrot.', 'Step 2: Go to the chest and collect gold ingots and convert them to gold nuggets.', 'Step 3: Go to the crafting table and surround the carrot with gold nuggets to create a golden carrot.']Recipe for beetroot_soup:\n['Step 1: Go to the farm and collect 6 beetroot.', 'Step 2: From your inventory or other agents get a bowl.', 'Step 3: Go to the crafting table and combine the 6 beetroot and 1 bowl to make beetroot soup.']"}, "difficulty_metrics": {"total_recipe_steps": 6, "blocked_agents_count": 0, "unique_target_items": 2, "overall_difficulty_score": 4, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"gold_ingot": 4, "bowl": 1}, "1": {"gold_ingot": 4}}}, "multiagent_cooking_1_cooked_rabbit_1_suspicious_stew_blocked_access_1": {"conversation": "Let's collaborate to make cooked_rabbit and suspicious_stew.", "agent_count": 2, "target": {"cooked_rabbit": 1, "suspicious_stew": 1}, "type": "cooking", "timeout": 300, "recipes": {"cooked_rabbit": ["Step 1: Kill a rabbit and pick up 1 raw rabbit that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 2: Go to furnace and use it to cook the raw rabbit."], "suspicious_stew": ["Step 1: Go to the farm and collect 1 red mushroom, 1 brown mushroom.", "Step 2: From your inventory or other agents get a bowl and 1 dandelion", "Step 3: Go to the crafting table and combine the mushrooms, dandelion, and bowl to make suspicious stew."]}, "blocked_access_to_recipe": ["1"], "goal": {"0": "Collaborate with agents around you to make 1 cooked_rabbit, 1 suspicious_stew. Recipe for cooked_rabbit:\n['Step 1: Kill a rabbit and pick up 1 raw rabbit that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 2: Go to furnace and use it to cook the raw rabbit.']Recipe for suspicious_stew:\n['Step 1: Go to the farm and collect 1 red mushroom, 1 brown mushroom.', 'Step 2: From your inventory or other agents get a bowl and 1 dandelion', 'Step 3: Go to the crafting table and combine the mushrooms, dandelion, and bowl to make suspicious stew.']", "1": "Collaborate with agents around you to make 1 cooked_rabbit, 1 suspicious_stew. "}, "difficulty_metrics": {"total_recipe_steps": 5, "blocked_agents_count": 1, "unique_target_items": 2, "overall_difficulty_score": 5, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"coal": 1, "dandelion": 1}, "1": {"bowl": 1}}}, "multiagent_cooking_1_beetroot_soup_1_cookie_blocked_access_1": {"conversation": "Let's collaborate to make beetroot_soup and cookie.", "agent_count": 2, "target": {"beetroot_soup": 1, "cookie": 1}, "type": "cooking", "timeout": 300, "recipes": {"beetroot_soup": ["Step 1: Go to the farm and collect 6 beetroot.", "Step 2: From your inventory or other agents get a bowl.", "Step 3: Go to the crafting table and combine the 6 beetroot and 1 bowl to make beetroot soup."], "cookie": ["Step 1: Go to the farm and collect 2 wheat.", "Step 2: Get 1 cocoa bean from your inventory or other agents.", "Step 3: Go to the crafting table and combine the wheat and cocoa bean to craft a cookie."]}, "blocked_access_to_recipe": ["1"], "goal": {"0": "Collaborate with agents around you to make 1 beetroot_soup, 1 cookie. Recipe for beetroot_soup:\n['Step 1: Go to the farm and collect 6 beetroot.', 'Step 2: From your inventory or other agents get a bowl.', 'Step 3: Go to the crafting table and combine the 6 beetroot and 1 bowl to make beetroot soup.']Recipe for cookie:\n['Step 1: Go to the farm and collect 2 wheat.', 'Step 2: Get 1 cocoa bean from your inventory or other agents.', 'Step 3: Go to the crafting table and combine the wheat and cocoa bean to craft a cookie.']", "1": "Collaborate with agents around you to make 1 beetroot_soup, 1 cookie. "}, "difficulty_metrics": {"total_recipe_steps": 6, "blocked_agents_count": 1, "unique_target_items": 2, "overall_difficulty_score": 5, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"bowl": 1}, "1": {"cocoa_beans": 1}}}, "multiagent_cooking_1_cooked_mutton_1_suspicious_stew_blocked_access_1": {"conversation": "Let's collaborate to make suspicious_stew and cooked_mutton.", "agent_count": 2, "target": {"suspicious_stew": 1, "cooked_mutton": 1}, "type": "cooking", "timeout": 300, "recipes": {"suspicious_stew": ["Step 1: Go to the farm and collect 1 red mushroom, 1 brown mushroom.", "Step 2: From your inventory or other agents get a bowl and 1 dandelion", "Step 3: Go to the crafting table and combine the mushrooms, dandelion, and bowl to make suspicious stew."], "cooked_mutton": ["Step 1: Kill a sheep and pick up 1 mutton that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the mutton."]}, "blocked_access_to_recipe": ["1"], "goal": {"0": "Collaborate with agents around you to make 1 suspicious_stew, 1 cooked_mutton. Recipe for suspicious_stew:\n['Step 1: Go to the farm and collect 1 red mushroom, 1 brown mushroom.', 'Step 2: From your inventory or other agents get a bowl and 1 dandelion', 'Step 3: Go to the crafting table and combine the mushrooms, dandelion, and bowl to make suspicious stew.']Recipe for cooked_mutton:\n['Step 1: Kill a sheep and pick up 1 mutton that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the mutton.']", "1": "Collaborate with agents around you to make 1 suspicious_stew, 1 cooked_mutton. "}, "difficulty_metrics": {"total_recipe_steps": 5, "blocked_agents_count": 1, "unique_target_items": 2, "overall_difficulty_score": 5, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"bowl": 1, "coal": 1}, "1": {"dandelion": 1}}}, "multiagent_cooking_1_cooked_rabbit_1_golden_carrot": {"conversation": "Let's collaborate to make cooked_rabbit and golden_carrot.", "agent_count": 2, "target": {"cooked_rabbit": 1, "golden_carrot": 1}, "type": "cooking", "timeout": 300, "recipes": {"cooked_rabbit": ["Step 1: Kill a rabbit and pick up 1 raw rabbit that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 2: Go to furnace and use it to cook the raw rabbit."], "golden_carrot": ["Step 1: Go to the farm and collect 1 carrot.", "Step 2: Go to the chest and collect gold ingots and convert them to gold nuggets.", "Step 3: Go to the crafting table and surround the carrot with gold nuggets to create a golden carrot."]}, "blocked_access_to_recipe": [], "goal": {"0": "Collaborate with agents around you to make 1 cooked_rabbit, 1 golden_carrot. Recipe for cooked_rabbit:\n['Step 1: Kill a rabbit and pick up 1 raw rabbit that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 2: Go to furnace and use it to cook the raw rabbit.']Recipe for golden_carrot:\n['Step 1: Go to the farm and collect 1 carrot.', 'Step 2: Go to the chest and collect gold ingots and convert them to gold nuggets.', 'Step 3: Go to the crafting table and surround the carrot with gold nuggets to create a golden carrot.']", "1": "Collaborate with agents around you to make 1 cooked_rabbit, 1 golden_carrot. Recipe for cooked_rabbit:\n['Step 1: Kill a rabbit and pick up 1 raw rabbit that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 2: Go to furnace and use it to cook the raw rabbit.']Recipe for golden_carrot:\n['Step 1: Go to the farm and collect 1 carrot.', 'Step 2: Go to the chest and collect gold ingots and convert them to gold nuggets.', 'Step 3: Go to the crafting table and surround the carrot with gold nuggets to create a golden carrot.']"}, "difficulty_metrics": {"total_recipe_steps": 5, "blocked_agents_count": 0, "unique_target_items": 2, "overall_difficulty_score": 4, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"coal": 1, "gold_ingot": 4}, "1": {"gold_ingot": 4}}}, "multiagent_cooking_1_cooked_rabbit_1_pumpkin_pie_blocked_access_0_1": {"conversation": "Let's collaborate to make cooked_rabbit and pumpkin_pie.", "agent_count": 2, "target": {"cooked_rabbit": 1, "pumpkin_pie": 1}, "type": "cooking", "timeout": 300, "recipes": {"cooked_rabbit": ["Step 1: Kill a rabbit and pick up 1 raw rabbit that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 2: Go to furnace and use it to cook the raw rabbit."], "pumpkin_pie": ["Step 1: Go to the farm and collect 1 pumpkin and 1 sugar cane.", "Step 2: Get 1 egg from your inventory or other bots", "Step 3: Go to the crafting table and craft the sugar cane into sugar.", "Step 4: Go to the crafting table and combine the pumpkin, egg, and sugar to make a pumpkin pie."]}, "blocked_access_to_recipe": ["0", "1"], "goal": {"0": "Collaborate with agents around you to make 1 cooked_rabbit, 1 pumpkin_pie. ", "1": "Collaborate with agents around you to make 1 cooked_rabbit, 1 pumpkin_pie. "}, "difficulty_metrics": {"total_recipe_steps": 6, "blocked_agents_count": 2, "unique_target_items": 2, "overall_difficulty_score": 6, "difficulty_category": "hard"}, "difficulty": "hard", "initial_inventory": {"0": {"coal": 1}, "1": {"egg": 1}}}, "multiagent_cooking_1_cooked_mutton_1_cookie_blocked_access_0_1": {"conversation": "Let's collaborate to make cooked_mutton and cookie.", "agent_count": 2, "target": {"cooked_mutton": 1, "cookie": 1}, "type": "cooking", "timeout": 300, "recipes": {"cooked_mutton": ["Step 1: Kill a sheep and pick up 1 mutton that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the mutton."], "cookie": ["Step 1: Go to the farm and collect 2 wheat.", "Step 2: Get 1 cocoa bean from your inventory or other agents.", "Step 3: Go to the crafting table and combine the wheat and cocoa bean to craft a cookie."]}, "blocked_access_to_recipe": ["0", "1"], "goal": {"0": "Collaborate with agents around you to make 1 cooked_mutton, 1 cookie. ", "1": "Collaborate with agents around you to make 1 cooked_mutton, 1 cookie. "}, "difficulty_metrics": {"total_recipe_steps": 5, "blocked_agents_count": 2, "unique_target_items": 2, "overall_difficulty_score": 6, "difficulty_category": "hard"}, "difficulty": "hard", "initial_inventory": {"0": {"coal": 1}, "1": {"cocoa_beans": 1}}}, "multiagent_cooking_1_golden_carrot_1_suspicious_stew": {"conversation": "Let's collaborate to make golden_carrot and suspicious_stew.", "agent_count": 2, "target": {"golden_carrot": 1, "suspicious_stew": 1}, "type": "cooking", "timeout": 300, "recipes": {"golden_carrot": ["Step 1: Go to the farm and collect 1 carrot.", "Step 2: Go to the chest and collect gold ingots and convert them to gold nuggets.", "Step 3: Go to the crafting table and surround the carrot with gold nuggets to create a golden carrot."], "suspicious_stew": ["Step 1: Go to the farm and collect 1 red mushroom, 1 brown mushroom.", "Step 2: From your inventory or other agents get a bowl and 1 dandelion", "Step 3: Go to the crafting table and combine the mushrooms, dandelion, and bowl to make suspicious stew."]}, "blocked_access_to_recipe": [], "goal": {"0": "Collaborate with agents around you to make 1 golden_carrot, 1 suspicious_stew. Recipe for golden_carrot:\n['Step 1: Go to the farm and collect 1 carrot.', 'Step 2: Go to the chest and collect gold ingots and convert them to gold nuggets.', 'Step 3: Go to the crafting table and surround the carrot with gold nuggets to create a golden carrot.']Recipe for suspicious_stew:\n['Step 1: Go to the farm and collect 1 red mushroom, 1 brown mushroom.', 'Step 2: From your inventory or other agents get a bowl and 1 dandelion', 'Step 3: Go to the crafting table and combine the mushrooms, dandelion, and bowl to make suspicious stew.']", "1": "Collaborate with agents around you to make 1 golden_carrot, 1 suspicious_stew. Recipe for golden_carrot:\n['Step 1: Go to the farm and collect 1 carrot.', 'Step 2: Go to the chest and collect gold ingots and convert them to gold nuggets.', 'Step 3: Go to the crafting table and surround the carrot with gold nuggets to create a golden carrot.']Recipe for suspicious_stew:\n['Step 1: Go to the farm and collect 1 red mushroom, 1 brown mushroom.', 'Step 2: From your inventory or other agents get a bowl and 1 dandelion', 'Step 3: Go to the crafting table and combine the mushrooms, dandelion, and bowl to make suspicious stew.']"}, "difficulty_metrics": {"total_recipe_steps": 6, "blocked_agents_count": 0, "unique_target_items": 2, "overall_difficulty_score": 4, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"gold_ingot": 4, "bowl": 1}, "1": {"gold_ingot": 4, "dandelion": 1}}}, "multiagent_cooking_1_golden_carrot_1_pumpkin_pie_blocked_access_1": {"conversation": "Let's collaborate to make pumpkin_pie and golden_carrot.", "agent_count": 2, "target": {"pumpkin_pie": 1, "golden_carrot": 1}, "type": "cooking", "timeout": 300, "recipes": {"pumpkin_pie": ["Step 1: Go to the farm and collect 1 pumpkin and 1 sugar cane.", "Step 2: Get 1 egg from your inventory or other bots", "Step 3: Go to the crafting table and craft the sugar cane into sugar.", "Step 4: Go to the crafting table and combine the pumpkin, egg, and sugar to make a pumpkin pie."], "golden_carrot": ["Step 1: Go to the farm and collect 1 carrot.", "Step 2: Go to the chest and collect gold ingots and convert them to gold nuggets.", "Step 3: Go to the crafting table and surround the carrot with gold nuggets to create a golden carrot."]}, "blocked_access_to_recipe": ["1"], "goal": {"0": "Collaborate with agents around you to make 1 pumpkin_pie, 1 golden_carrot. Recipe for pumpkin_pie:\n['Step 1: Go to the farm and collect 1 pumpkin and 1 sugar cane.', 'Step 2: Get 1 egg from your inventory or other bots', 'Step 3: Go to the crafting table and craft the sugar cane into sugar.', 'Step 4: Go to the crafting table and combine the pumpkin, egg, and sugar to make a pumpkin pie.']Recipe for golden_carrot:\n['Step 1: Go to the farm and collect 1 carrot.', 'Step 2: Go to the chest and collect gold ingots and convert them to gold nuggets.', 'Step 3: Go to the crafting table and surround the carrot with gold nuggets to create a golden carrot.']", "1": "Collaborate with agents around you to make 1 pumpkin_pie, 1 golden_carrot. "}, "difficulty_metrics": {"total_recipe_steps": 7, "blocked_agents_count": 1, "unique_target_items": 2, "overall_difficulty_score": 5, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"egg": 1, "gold_ingot": 4}, "1": {"gold_ingot": 4}}}, "multiagent_cooking_1_cooked_mutton_1_cooked_porkchop_blocked_access_1": {"conversation": "Let's collaborate to make cooked_porkchop and cooked_mutton.", "agent_count": 2, "target": {"cooked_porkchop": 1, "cooked_mutton": 1}, "type": "cooking", "timeout": 300, "recipes": {"cooked_porkchop": ["Step 1: Kill a pig and pick up 1 porkchop that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the porkchop."], "cooked_mutton": ["Step 1: Kill a sheep and pick up 1 mutton that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the mutton."]}, "blocked_access_to_recipe": ["1"], "goal": {"0": "Collaborate with agents around you to make 1 cooked_porkchop, 1 cooked_mutton. Recipe for cooked_porkchop:\n['Step 1: Kill a pig and pick up 1 porkchop that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the porkchop.']Recipe for cooked_mutton:\n['Step 1: Kill a sheep and pick up 1 mutton that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the mutton.']", "1": "Collaborate with agents around you to make 1 cooked_porkchop, 1 cooked_mutton. "}, "difficulty_metrics": {"total_recipe_steps": 4, "blocked_agents_count": 1, "unique_target_items": 2, "overall_difficulty_score": 4, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"coal": 1}, "1": {"coal": 1}}}, "multiagent_cooking_1_cooked_porkchop_1_golden_carrot": {"conversation": "Let's collaborate to make golden_carrot and cooked_porkchop.", "agent_count": 2, "target": {"golden_carrot": 1, "cooked_porkchop": 1}, "type": "cooking", "timeout": 300, "recipes": {"golden_carrot": ["Step 1: Go to the farm and collect 1 carrot.", "Step 2: Go to the chest and collect gold ingots and convert them to gold nuggets.", "Step 3: Go to the crafting table and surround the carrot with gold nuggets to create a golden carrot."], "cooked_porkchop": ["Step 1: Kill a pig and pick up 1 porkchop that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the porkchop."]}, "blocked_access_to_recipe": [], "goal": {"0": "Collaborate with agents around you to make 1 golden_carrot, 1 cooked_porkchop. Recipe for golden_carrot:\n['Step 1: Go to the farm and collect 1 carrot.', 'Step 2: Go to the chest and collect gold ingots and convert them to gold nuggets.', 'Step 3: Go to the crafting table and surround the carrot with gold nuggets to create a golden carrot.']Recipe for cooked_porkchop:\n['Step 1: Kill a pig and pick up 1 porkchop that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the porkchop.']", "1": "Collaborate with agents around you to make 1 golden_carrot, 1 cooked_porkchop. Recipe for golden_carrot:\n['Step 1: Go to the farm and collect 1 carrot.', 'Step 2: Go to the chest and collect gold ingots and convert them to gold nuggets.', 'Step 3: Go to the crafting table and surround the carrot with gold nuggets to create a golden carrot.']Recipe for cooked_porkchop:\n['Step 1: Kill a pig and pick up 1 porkchop that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the porkchop.']"}, "difficulty_metrics": {"total_recipe_steps": 5, "blocked_agents_count": 0, "unique_target_items": 2, "overall_difficulty_score": 4, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"gold_ingot": 4, "coal": 1}, "1": {"gold_ingot": 4}}}, "multiagent_cooking_1_cooked_chicken_1_cooked_porkchop_blocked_access_1": {"conversation": "Let's collaborate to make cooked_chicken and cooked_porkchop.", "agent_count": 2, "target": {"cooked_chicken": 1, "cooked_porkchop": 1}, "type": "cooking", "timeout": 300, "recipes": {"cooked_chicken": ["Step 1: Kill a chicken and pick up 1 raw chicken that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the raw chicken."], "cooked_porkchop": ["Step 1: Kill a pig and pick up 1 porkchop that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the porkchop."]}, "blocked_access_to_recipe": ["1"], "goal": {"0": "Collaborate with agents around you to make 1 cooked_chicken, 1 cooked_porkchop. Recipe for cooked_chicken:\n['Step 1: Kill a chicken and pick up 1 raw chicken that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the raw chicken.']Recipe for cooked_porkchop:\n['Step 1: Kill a pig and pick up 1 porkchop that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the porkchop.']", "1": "Collaborate with agents around you to make 1 cooked_chicken, 1 cooked_porkchop. "}, "difficulty_metrics": {"total_recipe_steps": 4, "blocked_agents_count": 1, "unique_target_items": 2, "overall_difficulty_score": 4, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"coal": 1}, "1": {"coal": 1}}}, "multiagent_cooking_1_beetroot_soup_1_suspicious_stew_blocked_access_0_1": {"conversation": "Let's collaborate to make suspicious_stew and beetroot_soup.", "agent_count": 2, "target": {"suspicious_stew": 1, "beetroot_soup": 1}, "type": "cooking", "timeout": 300, "recipes": {"suspicious_stew": ["Step 1: Go to the farm and collect 1 red mushroom, 1 brown mushroom.", "Step 2: From your inventory or other agents get a bowl and 1 dandelion", "Step 3: Go to the crafting table and combine the mushrooms, dandelion, and bowl to make suspicious stew."], "beetroot_soup": ["Step 1: Go to the farm and collect 6 beetroot.", "Step 2: From your inventory or other agents get a bowl.", "Step 3: Go to the crafting table and combine the 6 beetroot and 1 bowl to make beetroot soup."]}, "blocked_access_to_recipe": ["0", "1"], "goal": {"0": "Collaborate with agents around you to make 1 suspicious_stew, 1 beetroot_soup. ", "1": "Collaborate with agents around you to make 1 suspicious_stew, 1 beetroot_soup. "}, "difficulty_metrics": {"total_recipe_steps": 6, "blocked_agents_count": 2, "unique_target_items": 2, "overall_difficulty_score": 6, "difficulty_category": "hard"}, "difficulty": "hard", "initial_inventory": {"0": {"bowl": 1, "dandelion": 1}, "1": {"bowl": 1}}}, "multiagent_cooking_1_cooked_mutton_1_cooked_rabbit": {"conversation": "Let's collaborate to make cooked_rabbit and cooked_mutton.", "agent_count": 2, "target": {"cooked_rabbit": 1, "cooked_mutton": 1}, "type": "cooking", "timeout": 300, "recipes": {"cooked_rabbit": ["Step 1: Kill a rabbit and pick up 1 raw rabbit that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 2: Go to furnace and use it to cook the raw rabbit."], "cooked_mutton": ["Step 1: Kill a sheep and pick up 1 mutton that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the mutton."]}, "blocked_access_to_recipe": [], "goal": {"0": "Collaborate with agents around you to make 1 cooked_rabbit, 1 cooked_mutton. Recipe for cooked_rabbit:\n['Step 1: Kill a rabbit and pick up 1 raw rabbit that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 2: Go to furnace and use it to cook the raw rabbit.']Recipe for cooked_mutton:\n['Step 1: Kill a sheep and pick up 1 mutton that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the mutton.']", "1": "Collaborate with agents around you to make 1 cooked_rabbit, 1 cooked_mutton. Recipe for cooked_rabbit:\n['Step 1: Kill a rabbit and pick up 1 raw rabbit that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 2: Go to furnace and use it to cook the raw rabbit.']Recipe for cooked_mutton:\n['Step 1: Kill a sheep and pick up 1 mutton that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the mutton.']"}, "difficulty_metrics": {"total_recipe_steps": 4, "blocked_agents_count": 0, "unique_target_items": 2, "overall_difficulty_score": 3, "difficulty_category": "easy"}, "difficulty": "easy", "initial_inventory": {"0": {"coal": 1}, "1": {"coal": 1}}}, "multiagent_cooking_1_beetroot_soup_1_cooked_chicken_blocked_access_0": {"conversation": "Let's collaborate to make beetroot_soup and cooked_chicken.", "agent_count": 2, "target": {"beetroot_soup": 1, "cooked_chicken": 1}, "type": "cooking", "timeout": 300, "recipes": {"beetroot_soup": ["Step 1: Go to the farm and collect 6 beetroot.", "Step 2: From your inventory or other agents get a bowl.", "Step 3: Go to the crafting table and combine the 6 beetroot and 1 bowl to make beetroot soup."], "cooked_chicken": ["Step 1: Kill a chicken and pick up 1 raw chicken that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the raw chicken."]}, "blocked_access_to_recipe": ["0"], "goal": {"0": "Collaborate with agents around you to make 1 beetroot_soup, 1 cooked_chicken. ", "1": "Collaborate with agents around you to make 1 beetroot_soup, 1 cooked_chicken. Recipe for beetroot_soup:\n['Step 1: Go to the farm and collect 6 beetroot.', 'Step 2: From your inventory or other agents get a bowl.', 'Step 3: Go to the crafting table and combine the 6 beetroot and 1 bowl to make beetroot soup.']Recipe for cooked_chicken:\n['Step 1: Kill a chicken and pick up 1 raw chicken that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the raw chicken.']"}, "difficulty_metrics": {"total_recipe_steps": 5, "blocked_agents_count": 1, "unique_target_items": 2, "overall_difficulty_score": 5, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"bowl": 1}, "1": {"coal": 1}}}, "multiagent_cooking_1_cooked_chicken_1_cooked_mutton_blocked_access_0": {"conversation": "Let's collaborate to make cooked_chicken and cooked_mutton.", "agent_count": 2, "target": {"cooked_chicken": 1, "cooked_mutton": 1}, "type": "cooking", "timeout": 300, "recipes": {"cooked_chicken": ["Step 1: Kill a chicken and pick up 1 raw chicken that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the raw chicken."], "cooked_mutton": ["Step 1: Kill a sheep and pick up 1 mutton that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the mutton."]}, "blocked_access_to_recipe": ["0"], "goal": {"0": "Collaborate with agents around you to make 1 cooked_chicken, 1 cooked_mutton. ", "1": "Collaborate with agents around you to make 1 cooked_chicken, 1 cooked_mutton. Recipe for cooked_chicken:\n['Step 1: Kill a chicken and pick up 1 raw chicken that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the raw chicken.']Recipe for cooked_mutton:\n['Step 1: Kill a sheep and pick up 1 mutton that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the mutton.']"}, "difficulty_metrics": {"total_recipe_steps": 4, "blocked_agents_count": 1, "unique_target_items": 2, "overall_difficulty_score": 4, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"coal": 1}, "1": {"coal": 1}}}, "multiagent_cooking_1_beetroot_soup_1_cooked_mutton_blocked_access_0_1": {"conversation": "Let's collaborate to make cooked_mutton and beetroot_soup.", "agent_count": 2, "target": {"cooked_mutton": 1, "beetroot_soup": 1}, "type": "cooking", "timeout": 300, "recipes": {"cooked_mutton": ["Step 1: Kill a sheep and pick up 1 mutton that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the mutton."], "beetroot_soup": ["Step 1: Go to the farm and collect 6 beetroot.", "Step 2: From your inventory or other agents get a bowl.", "Step 3: Go to the crafting table and combine the 6 beetroot and 1 bowl to make beetroot soup."]}, "blocked_access_to_recipe": ["0", "1"], "goal": {"0": "Collaborate with agents around you to make 1 cooked_mutton, 1 beetroot_soup. ", "1": "Collaborate with agents around you to make 1 cooked_mutton, 1 beetroot_soup. "}, "difficulty_metrics": {"total_recipe_steps": 5, "blocked_agents_count": 2, "unique_target_items": 2, "overall_difficulty_score": 6, "difficulty_category": "hard"}, "difficulty": "hard", "initial_inventory": {"0": {"coal": 1}, "1": {"bowl": 1}}}, "multiagent_cooking_1_cooked_rabbit_1_suspicious_stew_blocked_access_0": {"conversation": "Let's collaborate to make suspicious_stew and cooked_rabbit.", "agent_count": 2, "target": {"suspicious_stew": 1, "cooked_rabbit": 1}, "type": "cooking", "timeout": 300, "recipes": {"suspicious_stew": ["Step 1: Go to the farm and collect 1 red mushroom, 1 brown mushroom.", "Step 2: From your inventory or other agents get a bowl and 1 dandelion", "Step 3: Go to the crafting table and combine the mushrooms, dandelion, and bowl to make suspicious stew."], "cooked_rabbit": ["Step 1: Kill a rabbit and pick up 1 raw rabbit that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 2: Go to furnace and use it to cook the raw rabbit."]}, "blocked_access_to_recipe": ["0"], "goal": {"0": "Collaborate with agents around you to make 1 suspicious_stew, 1 cooked_rabbit. ", "1": "Collaborate with agents around you to make 1 suspicious_stew, 1 cooked_rabbit. Recipe for suspicious_stew:\n['Step 1: Go to the farm and collect 1 red mushroom, 1 brown mushroom.', 'Step 2: From your inventory or other agents get a bowl and 1 dandelion', 'Step 3: Go to the crafting table and combine the mushrooms, dandelion, and bowl to make suspicious stew.']Recipe for cooked_rabbit:\n['Step 1: Kill a rabbit and pick up 1 raw rabbit that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 2: Go to furnace and use it to cook the raw rabbit.']"}, "difficulty_metrics": {"total_recipe_steps": 5, "blocked_agents_count": 1, "unique_target_items": 2, "overall_difficulty_score": 5, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"bowl": 1, "coal": 1}, "1": {"dandelion": 1}}}, "multiagent_cooking_1_cooked_mutton_1_cookie": {"conversation": "Let's collaborate to make cookie and cooked_mutton.", "agent_count": 2, "target": {"cookie": 1, "cooked_mutton": 1}, "type": "cooking", "timeout": 300, "recipes": {"cookie": ["Step 1: Go to the farm and collect 2 wheat.", "Step 2: Get 1 cocoa bean from your inventory or other agents.", "Step 3: Go to the crafting table and combine the wheat and cocoa bean to craft a cookie."], "cooked_mutton": ["Step 1: Kill a sheep and pick up 1 mutton that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the mutton."]}, "blocked_access_to_recipe": [], "goal": {"0": "Collaborate with agents around you to make 1 cookie, 1 cooked_mutton. Recipe for cookie:\n['Step 1: Go to the farm and collect 2 wheat.', 'Step 2: Get 1 cocoa bean from your inventory or other agents.', 'Step 3: Go to the crafting table and combine the wheat and cocoa bean to craft a cookie.']Recipe for cooked_mutton:\n['Step 1: Kill a sheep and pick up 1 mutton that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the mutton.']", "1": "Collaborate with agents around you to make 1 cookie, 1 cooked_mutton. Recipe for cookie:\n['Step 1: Go to the farm and collect 2 wheat.', 'Step 2: Get 1 cocoa bean from your inventory or other agents.', 'Step 3: Go to the crafting table and combine the wheat and cocoa bean to craft a cookie.']Recipe for cooked_mutton:\n['Step 1: Kill a sheep and pick up 1 mutton that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the mutton.']"}, "difficulty_metrics": {"total_recipe_steps": 5, "blocked_agents_count": 0, "unique_target_items": 2, "overall_difficulty_score": 4, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"cocoa_beans": 1}, "1": {"coal": 1}}}, "multiagent_cooking_1_cooked_mutton_1_cooked_porkchop": {"conversation": "Let's collaborate to make cooked_mutton and cooked_porkchop.", "agent_count": 2, "target": {"cooked_mutton": 1, "cooked_porkchop": 1}, "type": "cooking", "timeout": 300, "recipes": {"cooked_mutton": ["Step 1: Kill a sheep and pick up 1 mutton that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the mutton."], "cooked_porkchop": ["Step 1: Kill a pig and pick up 1 porkchop that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the porkchop."]}, "blocked_access_to_recipe": [], "goal": {"0": "Collaborate with agents around you to make 1 cooked_mutton, 1 cooked_porkchop. Recipe for cooked_mutton:\n['Step 1: Kill a sheep and pick up 1 mutton that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the mutton.']Recipe for cooked_porkchop:\n['Step 1: Kill a pig and pick up 1 porkchop that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the porkchop.']", "1": "Collaborate with agents around you to make 1 cooked_mutton, 1 cooked_porkchop. Recipe for cooked_mutton:\n['Step 1: Kill a sheep and pick up 1 mutton that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the mutton.']Recipe for cooked_porkchop:\n['Step 1: Kill a pig and pick up 1 porkchop that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the porkchop.']"}, "difficulty_metrics": {"total_recipe_steps": 4, "blocked_agents_count": 0, "unique_target_items": 2, "overall_difficulty_score": 3, "difficulty_category": "easy"}, "difficulty": "easy", "initial_inventory": {"0": {"coal": 1}, "1": {"coal": 1}}}, "multiagent_cooking_1_cooked_chicken_1_cooked_mutton_blocked_access_1": {"conversation": "Let's collaborate to make cooked_mutton and cooked_chicken.", "agent_count": 2, "target": {"cooked_mutton": 1, "cooked_chicken": 1}, "type": "cooking", "timeout": 300, "recipes": {"cooked_mutton": ["Step 1: Kill a sheep and pick up 1 mutton that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the mutton."], "cooked_chicken": ["Step 1: Kill a chicken and pick up 1 raw chicken that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the raw chicken."]}, "blocked_access_to_recipe": ["1"], "goal": {"0": "Collaborate with agents around you to make 1 cooked_mutton, 1 cooked_chicken. Recipe for cooked_mutton:\n['Step 1: Kill a sheep and pick up 1 mutton that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the mutton.']Recipe for cooked_chicken:\n['Step 1: Kill a chicken and pick up 1 raw chicken that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the raw chicken.']", "1": "Collaborate with agents around you to make 1 cooked_mutton, 1 cooked_chicken. "}, "difficulty_metrics": {"total_recipe_steps": 4, "blocked_agents_count": 1, "unique_target_items": 2, "overall_difficulty_score": 4, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"coal": 1}, "1": {"coal": 1}}}, "multiagent_cooking_1_mushroom_stew_1_suspicious_stew": {"conversation": "Let's collaborate to make suspicious_stew and mushroom_stew.", "agent_count": 2, "target": {"suspicious_stew": 1, "mushroom_stew": 1}, "type": "cooking", "timeout": 300, "recipes": {"suspicious_stew": ["Step 1: Go to the farm and collect 1 red mushroom, 1 brown mushroom.", "Step 2: From your inventory or other agents get a bowl and 1 dandelion", "Step 3: Go to the crafting table and combine the mushrooms, dandelion, and bowl to make suspicious stew."], "mushroom_stew": ["Step 1: Go to the farm and collect 1 red mushroom and 1 brown mushroom.", "Step 2: From your inventory or other agents get a bowl.", "Step 3: Go to the crafting table and combine both the mushrooms and bowl to make mushroom stew."]}, "blocked_access_to_recipe": [], "goal": {"0": "Collaborate with agents around you to make 1 suspicious_stew, 1 mushroom_stew. Recipe for suspicious_stew:\n['Step 1: Go to the farm and collect 1 red mushroom, 1 brown mushroom.', 'Step 2: From your inventory or other agents get a bowl and 1 dandelion', 'Step 3: Go to the crafting table and combine the mushrooms, dandelion, and bowl to make suspicious stew.']Recipe for mushroom_stew:\n['Step 1: Go to the farm and collect 1 red mushroom and 1 brown mushroom.', 'Step 2: From your inventory or other agents get a bowl.', 'Step 3: Go to the crafting table and combine both the mushrooms and bowl to make mushroom stew.']", "1": "Collaborate with agents around you to make 1 suspicious_stew, 1 mushroom_stew. Recipe for suspicious_stew:\n['Step 1: Go to the farm and collect 1 red mushroom, 1 brown mushroom.', 'Step 2: From your inventory or other agents get a bowl and 1 dandelion', 'Step 3: Go to the crafting table and combine the mushrooms, dandelion, and bowl to make suspicious stew.']Recipe for mushroom_stew:\n['Step 1: Go to the farm and collect 1 red mushroom and 1 brown mushroom.', 'Step 2: From your inventory or other agents get a bowl.', 'Step 3: Go to the crafting table and combine both the mushrooms and bowl to make mushroom stew.']"}, "difficulty_metrics": {"total_recipe_steps": 6, "blocked_agents_count": 0, "unique_target_items": 2, "overall_difficulty_score": 4, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"bowl": 1, "dandelion": 1}, "1": {"bowl": 1}}}, "multiagent_cooking_1_beetroot_soup_1_cooked_rabbit_blocked_access_1": {"conversation": "Let's collaborate to make cooked_rabbit and beetroot_soup.", "agent_count": 2, "target": {"cooked_rabbit": 1, "beetroot_soup": 1}, "type": "cooking", "timeout": 300, "recipes": {"cooked_rabbit": ["Step 1: Kill a rabbit and pick up 1 raw rabbit that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 2: Go to furnace and use it to cook the raw rabbit."], "beetroot_soup": ["Step 1: Go to the farm and collect 6 beetroot.", "Step 2: From your inventory or other agents get a bowl.", "Step 3: Go to the crafting table and combine the 6 beetroot and 1 bowl to make beetroot soup."]}, "blocked_access_to_recipe": ["1"], "goal": {"0": "Collaborate with agents around you to make 1 cooked_rabbit, 1 beetroot_soup. Recipe for cooked_rabbit:\n['Step 1: Kill a rabbit and pick up 1 raw rabbit that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 2: Go to furnace and use it to cook the raw rabbit.']Recipe for beetroot_soup:\n['Step 1: Go to the farm and collect 6 beetroot.', 'Step 2: From your inventory or other agents get a bowl.', 'Step 3: Go to the crafting table and combine the 6 beetroot and 1 bowl to make beetroot soup.']", "1": "Collaborate with agents around you to make 1 cooked_rabbit, 1 beetroot_soup. "}, "difficulty_metrics": {"total_recipe_steps": 5, "blocked_agents_count": 1, "unique_target_items": 2, "overall_difficulty_score": 5, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"coal": 1}, "1": {"bowl": 1}}}}