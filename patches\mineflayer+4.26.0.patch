diff --git a/node_modules/mineflayer/lib/plugins/place_block.js b/node_modules/mineflayer/lib/plugins/place_block.js
index fdaec6b..08983b6 100644
--- a/node_modules/mineflayer/lib/plugins/place_block.js
+++ b/node_modules/mineflayer/lib/plugins/place_block.js
@@ -11,7 +11,7 @@ function inject (bot) {
     let newBlock = bot.blockAt(dest)
     if (oldBlock.type === newBlock.type) {
       [oldBlock, newBlock] = await onceWithCleanup(bot, `blockUpdate:${dest}`, {
-        timeout: 5000,
+        timeout: 500,
         // Condition to wait to receive block update actually changing the block type, in case the bot receives block updates with no changes
         // oldBlock and newBlock will both be null when the world unloads
         checkCondition: (oldBlock, newBlock) => !oldBlock || !newBlock || oldBlock.type !== newBlock.type
