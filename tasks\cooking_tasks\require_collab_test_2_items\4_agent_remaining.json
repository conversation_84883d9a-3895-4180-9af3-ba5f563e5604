{"multiagent_cooking_2_1_bread_1_cake": {"conversation": "Let's work together to make cake, bread.", "agent_count": 4, "target": {"cake": 1, "bread": 1}, "type": "cooking", "timeout": 500, "recipes": {"cake": ["Step 1: Go to the farm and collect 3 wheat, 2 sugar cane.", "Step 2: From your inventory or other agents get 3 milk buckets (already filled with milk).", "Step 3: Get an egg from your inventory or other agents.", "Step 4: Go to the crafting table and craft the sugarcane into sugar.", "Step 5: Go to the crafting table and combine all ingredients (3 wheat, 2 sugar, 1 egg, and milk bucket) to make a cake."], "bread": ["Step 1: Go to the farm and collect 3 wheat.", "Step 2: Go to the crafting table and use the wheat to craft bread."]}, "blocked_access_to_recipe": [], "goal": {"0": "Collaborate with agents around you to make 1 cake, 1 bread. Recipe for cake:\n['Step 1: Go to the farm and collect 3 wheat, 2 sugar cane.', 'Step 2: From your inventory or other agents get 3 milk buckets (already filled with milk).', 'Step 3: Get an egg from your inventory or other agents.', 'Step 4: Go to the crafting table and craft the sugarcane into sugar.', 'Step 5: Go to the crafting table and combine all ingredients (3 wheat, 2 sugar, 1 egg, and milk bucket) to make a cake.']Recipe for bread:\n['Step 1: Go to the farm and collect 3 wheat.', 'Step 2: Go to the crafting table and use the wheat to craft bread.']", "1": "Collaborate with agents around you to make 1 cake, 1 bread. Recipe for cake:\n['Step 1: Go to the farm and collect 3 wheat, 2 sugar cane.', 'Step 2: From your inventory or other agents get 3 milk buckets (already filled with milk).', 'Step 3: Get an egg from your inventory or other agents.', 'Step 4: Go to the crafting table and craft the sugarcane into sugar.', 'Step 5: Go to the crafting table and combine all ingredients (3 wheat, 2 sugar, 1 egg, and milk bucket) to make a cake.']Recipe for bread:\n['Step 1: Go to the farm and collect 3 wheat.', 'Step 2: Go to the crafting table and use the wheat to craft bread.']"}, "initial_inventory": {"0": {"milk_bucket": 1}, "1": {"milk_bucket": 1}, "2": {"milk_bucket": 1}, "3": {"egg": 1}}}, "multiagent_cooking_2_1_baked_potato_1_golden_apple": {"conversation": "Let's work together to make baked_potato, golden_apple.", "agent_count": 4, "target": {"baked_potato": 1, "golden_apple": 1}, "type": "cooking", "timeout": 500, "recipes": {"baked_potato": ["Step 1: Go to the farm and collect 1 potato (search for 'potatoes' (not 'potato')).", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 2: Go to the furnace and bake the potato."], "golden_apple": ["Step 1: Get 1 apple and 8 gold ingots from your inventory or other bots.", "Step 2: Go to the crafting table and surround the apple with the gold ingots to create a golden apple."]}, "blocked_access_to_recipe": [], "goal": {"0": "Collaborate with agents around you to make 1 baked_potato, 1 golden_apple. Recipe for baked_potato:\n[\"Step 1: Go to the farm and collect 1 potato (search for 'potatoes' (not 'potato')).\", 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 2: Go to the furnace and bake the potato.']Recipe for golden_apple:\n['Step 1: Get 1 apple and 8 gold ingots from your inventory or other bots.', 'Step 2: Go to the crafting table and surround the apple with the gold ingots to create a golden apple.']", "1": "Collaborate with agents around you to make 1 baked_potato, 1 golden_apple. Recipe for baked_potato:\n[\"Step 1: Go to the farm and collect 1 potato (search for 'potatoes' (not 'potato')).\", 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 2: Go to the furnace and bake the potato.']Recipe for golden_apple:\n['Step 1: Get 1 apple and 8 gold ingots from your inventory or other bots.', 'Step 2: Go to the crafting table and surround the apple with the gold ingots to create a golden apple.']"}, "initial_inventory": {"0": {"coal": 3, "gold_ingot": 2}, "1": {"coal": 3, "gold_ingot": 2}, "2": {"coal": 3, "gold_ingot": 2, "apple": 1}, "3": {"coal": 3, "gold_ingot": 2}}}, "multiagent_cooking_2_1_baked_potato_1_cake": {"conversation": "Let's work together to make baked_potato, cake.", "agent_count": 4, "target": {"baked_potato": 1, "cake": 1}, "type": "cooking", "timeout": 500, "recipes": {"baked_potato": ["Step 1: Go to the farm and collect 1 potato (search for 'potatoes' (not 'potato')).", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 2: Go to the furnace and bake the potato."], "cake": ["Step 1: Go to the farm and collect 3 wheat, 2 sugar cane.", "Step 2: From your inventory or other agents get 3 milk buckets (already filled with milk).", "Step 3: Get an egg from your inventory or other agents.", "Step 4: Go to the crafting table and craft the sugarcane into sugar.", "Step 5: Go to the crafting table and combine all ingredients (3 wheat, 2 sugar, 1 egg, and milk bucket) to make a cake."]}, "blocked_access_to_recipe": [], "goal": {"0": "Collaborate with agents around you to make 1 baked_potato, 1 cake. Recipe for baked_potato:\n[\"Step 1: Go to the farm and collect 1 potato (search for 'potatoes' (not 'potato')).\", 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 2: Go to the furnace and bake the potato.']Recipe for cake:\n['Step 1: Go to the farm and collect 3 wheat, 2 sugar cane.', 'Step 2: From your inventory or other agents get 3 milk buckets (already filled with milk).', 'Step 3: Get an egg from your inventory or other agents.', 'Step 4: Go to the crafting table and craft the sugarcane into sugar.', 'Step 5: Go to the crafting table and combine all ingredients (3 wheat, 2 sugar, 1 egg, and milk bucket) to make a cake.']", "1": "Collaborate with agents around you to make 1 baked_potato, 1 cake. Recipe for baked_potato:\n[\"Step 1: Go to the farm and collect 1 potato (search for 'potatoes' (not 'potato')).\", 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 2: Go to the furnace and bake the potato.']Recipe for cake:\n['Step 1: Go to the farm and collect 3 wheat, 2 sugar cane.', 'Step 2: From your inventory or other agents get 3 milk buckets (already filled with milk).', 'Step 3: Get an egg from your inventory or other agents.', 'Step 4: Go to the crafting table and craft the sugarcane into sugar.', 'Step 5: Go to the crafting table and combine all ingredients (3 wheat, 2 sugar, 1 egg, and milk bucket) to make a cake.']"}, "initial_inventory": {"0": {"coal": 3, "milk_bucket": 1}, "1": {"coal": 3, "milk_bucket": 1}, "2": {"coal": 3, "milk_bucket": 1}, "3": {"coal": 3, "egg": 1}}}, "multiagent_cooking_2_1_cooked_beef_1_golden_apple": {"conversation": "Let's work together to make golden_apple, cooked_beef.", "agent_count": 4, "target": {"golden_apple": 1, "cooked_beef": 1}, "type": "cooking", "timeout": 500, "recipes": {"golden_apple": ["Step 1: Get 1 apple and 8 gold ingots from your inventory or other bots.", "Step 2: Go to the crafting table and surround the apple with the gold ingots to create a golden apple."], "cooked_beef": ["Step 1: Kill a cow and pick up 1 beef that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the beef."]}, "blocked_access_to_recipe": [], "goal": {"0": "Collaborate with agents around you to make 1 golden_apple, 1 cooked_beef. Recipe for golden_apple:\n['Step 1: Get 1 apple and 8 gold ingots from your inventory or other bots.', 'Step 2: Go to the crafting table and surround the apple with the gold ingots to create a golden apple.']Recipe for cooked_beef:\n['Step 1: Kill a cow and pick up 1 beef that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the beef.']", "1": "Collaborate with agents around you to make 1 golden_apple, 1 cooked_beef. Recipe for golden_apple:\n['Step 1: Get 1 apple and 8 gold ingots from your inventory or other bots.', 'Step 2: Go to the crafting table and surround the apple with the gold ingots to create a golden apple.']Recipe for cooked_beef:\n['Step 1: Kill a cow and pick up 1 beef that is dropped.', 'Step 2: Get coal from your inventory or other agents.', 'Step 3: Put coal in the furnace', 'Step 4: Go to furnace and use it to cook the beef.']"}, "initial_inventory": {"0": {"gold_ingot": 2, "coal": 3}, "1": {"gold_ingot": 2, "apple": 1, "coal": 3}, "2": {"gold_ingot": 2, "coal": 3}, "3": {"gold_ingot": 2, "coal": 3}}}}