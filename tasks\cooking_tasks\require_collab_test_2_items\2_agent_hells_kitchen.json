{"multiagent_cooking_bread_golden_apple_hells_kitchen": {"conversation": "We need to make bread and golden_apple together. You are supposed to make golden_apple and I am supposed to make bread, but I only have YOUR recipe and you only have access to MY recipe! Let's exchange information and get cooking!", "agent_count": 2, "target": ["bread", "golden_apple"], "type": "cooking", "timeout": 300, "recipes": {"bread": ["Step 1: Go to the farm and collect 3 wheat.", "Step 2: Go to the crafting table and use the wheat to craft bread."], "golden_apple": ["Step 1: Get 1 apple and 8 gold ingots from your inventory or other bots.", "Step 2: Go to the crafting table and surround the apple with the gold ingots to create a golden apple."]}, "blocked_access_to_recipe": [], "goal": {"0": "You need to make bread, but you don't have the recipe for it, your partner has it!\n\nYour partner needs to make golden_apple. You have their recipe:\nRecipe for golden_apple:\nStep 1: Get 1 apple and 8 gold ingots from your inventory or other bots.\nStep 2: Go to the crafting table and surround the apple with the gold ingots to create a golden apple.\n\nYou must communicate effectively to exchange recipe information and complete both dishes. Note: You can only guide your partner with recipe steps. You cannot help with ingredient collection or cooking.", "1": "You need to make golden_apple, but you don't have the recipe for it, your partner has it!\n\nYour partner needs to make bread. You have their recipe:\nRecipe for bread:\nStep 1: Go to the farm and collect 3 wheat.\nStep 2: Go to the crafting table and use the wheat to craft bread.\n\nYou must communicate effectively to exchange recipe information and complete both dishes. Note: You can only guide your partner with recipe steps. You cannot help with ingredient collection or cooking."}, "task_type": "cooking", "difficulty_metrics": {"total_recipe_steps": 4, "max_steps_per_recipe": 2, "unique_target_items": 2, "overall_difficulty_score": 4, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"gold_ingot": 5}, "1": {"gold_ingot": 5, "apple": 1}}}, "multiagent_cooking_golden_apple_rabbit_stew_hells_kitchen": {"conversation": "We need to make golden_apple and rabbit_stew together. You are supposed to make rabbit_stew and I am supposed to make golden_apple, but I only have YOUR recipe and you only have access to MY recipe! Let's exchange information and get cooking!", "agent_count": 2, "target": ["golden_apple", "rabbit_stew"], "type": "cooking", "timeout": 300, "recipes": {"golden_apple": ["Step 1: Get 1 apple and 8 gold ingots from your inventory or other bots.", "Step 2: Go to the crafting table and surround the apple with the gold ingots to create a golden apple."], "rabbit_stew": ["Step 1: Go to the farm and collect 1 carrot, 1 potato, and 1 brown mushroom (search for 'potatoes' (not 'potato').", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to the furnace and bake the potato.", "Step 5: From your inventory or other agents get a bowl", "Step 6: Kill a rabbit and pick up 1 raw rabbit that is dropped.", "Step 7: Go to the furnace and cook the raw rabbit.", "Step 8: Go to the crafting table and combine the cooked rabbit, baked potato, carrot, brown mushroom, and bowl to make rabbit stew."]}, "blocked_access_to_recipe": [], "goal": {"0": "You need to make golden_apple, but you don't have the recipe for it, your partner has it!\n\nYour partner needs to make rabbit_stew. You have their recipe:\nRecipe for rabbit_stew:\nStep 1: Go to the farm and collect 1 carrot, 1 potato, and 1 brown mushroom (search for 'potatoes' (not 'potato').\nStep 2: Get coal from your inventory or other agents.\nStep 3: Put coal in the furnace\nStep 4: Go to the furnace and bake the potato.\nStep 5: From your inventory or other agents get a bowl\nStep 6: Kill a rabbit and pick up 1 raw rabbit that is dropped.\nStep 7: Go to the furnace and cook the raw rabbit.\nStep 8: Go to the crafting table and combine the cooked rabbit, baked potato, carrot, brown mushroom, and bowl to make rabbit stew.\n\nYou must communicate effectively to exchange recipe information and complete both dishes. Note: You can only guide your partner with recipe steps. You cannot help with ingredient collection or cooking.", "1": "You need to make rabbit_stew, but you don't have the recipe for it, your partner has it!\n\nYour partner needs to make golden_apple. You have their recipe:\nRecipe for golden_apple:\nStep 1: Get 1 apple and 8 gold ingots from your inventory or other bots.\nStep 2: Go to the crafting table and surround the apple with the gold ingots to create a golden apple.\n\nYou must communicate effectively to exchange recipe information and complete both dishes. Note: You can only guide your partner with recipe steps. You cannot help with ingredient collection or cooking."}, "task_type": "cooking", "difficulty_metrics": {"total_recipe_steps": 10, "max_steps_per_recipe": 8, "unique_target_items": 2, "overall_difficulty_score": 6, "difficulty_category": "hard"}, "difficulty": "hard", "initial_inventory": {"0": {"gold_ingot": 5, "bowl": 1}, "1": {"gold_ingot": 5, "apple": 1}}}, "multiagent_cooking_bread_cake_hells_kitchen": {"conversation": "We need to make bread and cake together. You are supposed to make cake and I am supposed to make bread, but I only have YOUR recipe and you only have access to MY recipe! Let's exchange information and get cooking!", "agent_count": 2, "target": ["bread", "cake"], "type": "cooking", "timeout": 300, "recipes": {"bread": ["Step 1: Go to the farm and collect 3 wheat.", "Step 2: Go to the crafting table and use the wheat to craft bread."], "cake": ["Step 1: Go to the farm and collect 3 wheat, 2 sugar cane.", "Step 2: From your inventory or other agents get 3 milk buckets (already filled with milk).", "Step 3: Get an egg from your inventory or other agents.", "Step 4: Go to the crafting table and craft the sugarcane into sugar.", "Step 5: Go to the crafting table and combine all ingredients (3 wheat, 2 sugar, 1 egg, and milk bucket) to make a cake."]}, "blocked_access_to_recipe": [], "goal": {"0": "You need to make bread, but you don't have the recipe for it, your partner has it!\n\nYour partner needs to make cake. You have their recipe:\nRecipe for cake:\nStep 1: Go to the farm and collect 3 wheat, 2 sugar cane.\nStep 2: From your inventory or other agents get 3 milk buckets (already filled with milk).\nStep 3: Get an egg from your inventory or other agents.\nStep 4: Go to the crafting table and craft the sugarcane into sugar.\nStep 5: Go to the crafting table and combine all ingredients (3 wheat, 2 sugar, 1 egg, and milk bucket) to make a cake.\n\nYou must communicate effectively to exchange recipe information and complete both dishes. Note: You can only guide your partner with recipe steps. You cannot help with ingredient collection or cooking.", "1": "You need to make cake, but you don't have the recipe for it, your partner has it!\n\nYour partner needs to make bread. You have their recipe:\nRecipe for bread:\nStep 1: Go to the farm and collect 3 wheat.\nStep 2: Go to the crafting table and use the wheat to craft bread.\n\nYou must communicate effectively to exchange recipe information and complete both dishes. Note: You can only guide your partner with recipe steps. You cannot help with ingredient collection or cooking."}, "task_type": "cooking", "difficulty_metrics": {"total_recipe_steps": 7, "max_steps_per_recipe": 5, "unique_target_items": 2, "overall_difficulty_score": 5, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"milk_bucket": 2}, "1": {"milk_bucket": 2, "egg": 1}}}, "multiagent_cooking_baked_potato_golden_apple_hells_kitchen": {"conversation": "We need to make baked_potato and golden_apple together. You are supposed to make golden_apple and I am supposed to make baked_potato, but I only have YOUR recipe and you only have access to MY recipe! Let's exchange information and get cooking!", "agent_count": 2, "target": ["baked_potato", "golden_apple"], "type": "cooking", "timeout": 300, "recipes": {"baked_potato": ["Step 1: Go to the farm and collect 1 potato (search for 'potatoes' (not 'potato')).", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 2: Go to the furnace and bake the potato."], "golden_apple": ["Step 1: Get 1 apple and 8 gold ingots from your inventory or other bots.", "Step 2: Go to the crafting table and surround the apple with the gold ingots to create a golden apple."]}, "blocked_access_to_recipe": [], "goal": {"0": "You need to make baked_potato, but you don't have the recipe for it, your partner has it!\n\nYour partner needs to make golden_apple. You have their recipe:\nRecipe for golden_apple:\nStep 1: Get 1 apple and 8 gold ingots from your inventory or other bots.\nStep 2: Go to the crafting table and surround the apple with the gold ingots to create a golden apple.\n\nYou must communicate effectively to exchange recipe information and complete both dishes. Note: You can only guide your partner with recipe steps. You cannot help with ingredient collection or cooking.", "1": "You need to make golden_apple, but you don't have the recipe for it, your partner has it!\n\nYour partner needs to make baked_potato. You have their recipe:\nRecipe for baked_potato:\nStep 1: Go to the farm and collect 1 potato (search for 'potatoes' (not 'potato')).\nStep 2: Get coal from your inventory or other agents.\nStep 3: Put coal in the furnace\nStep 2: Go to the furnace and bake the potato.\n\nYou must communicate effectively to exchange recipe information and complete both dishes. Note: You can only guide your partner with recipe steps. You cannot help with ingredient collection or cooking."}, "task_type": "cooking", "difficulty_metrics": {"total_recipe_steps": 6, "max_steps_per_recipe": 4, "unique_target_items": 2, "overall_difficulty_score": 5, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"coal": 1, "gold_ingot": 5, "apple": 1}, "1": {"coal": 1, "gold_ingot": 5}}}, "multiagent_cooking_baked_potato_cake_hells_kitchen": {"conversation": "We need to make baked_potato and cake together. You are supposed to make cake and I am supposed to make baked_potato, but I only have YOUR recipe and you only have access to MY recipe! Let's exchange information and get cooking!", "agent_count": 2, "target": ["baked_potato", "cake"], "type": "cooking", "timeout": 300, "recipes": {"baked_potato": ["Step 1: Go to the farm and collect 1 potato (search for 'potatoes' (not 'potato')).", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 2: Go to the furnace and bake the potato."], "cake": ["Step 1: Go to the farm and collect 3 wheat, 2 sugar cane.", "Step 2: From your inventory or other agents get 3 milk buckets (already filled with milk).", "Step 3: Get an egg from your inventory or other agents.", "Step 4: Go to the crafting table and craft the sugarcane into sugar.", "Step 5: Go to the crafting table and combine all ingredients (3 wheat, 2 sugar, 1 egg, and milk bucket) to make a cake."]}, "blocked_access_to_recipe": [], "goal": {"0": "You need to make baked_potato, but you don't have the recipe for it, your partner has it!\n\nYour partner needs to make cake. You have their recipe:\nRecipe for cake:\nStep 1: Go to the farm and collect 3 wheat, 2 sugar cane.\nStep 2: From your inventory or other agents get 3 milk buckets (already filled with milk).\nStep 3: Get an egg from your inventory or other agents.\nStep 4: Go to the crafting table and craft the sugarcane into sugar.\nStep 5: Go to the crafting table and combine all ingredients (3 wheat, 2 sugar, 1 egg, and milk bucket) to make a cake.\n\nYou must communicate effectively to exchange recipe information and complete both dishes. Note: You can only guide your partner with recipe steps. You cannot help with ingredient collection or cooking.", "1": "You need to make cake, but you don't have the recipe for it, your partner has it!\n\nYour partner needs to make baked_potato. You have their recipe:\nRecipe for baked_potato:\nStep 1: Go to the farm and collect 1 potato (search for 'potatoes' (not 'potato')).\nStep 2: Get coal from your inventory or other agents.\nStep 3: Put coal in the furnace\nStep 2: Go to the furnace and bake the potato.\n\nYou must communicate effectively to exchange recipe information and complete both dishes. Note: You can only guide your partner with recipe steps. You cannot help with ingredient collection or cooking."}, "task_type": "cooking", "difficulty_metrics": {"total_recipe_steps": 9, "max_steps_per_recipe": 5, "unique_target_items": 2, "overall_difficulty_score": 6, "difficulty_category": "hard"}, "difficulty": "hard", "initial_inventory": {"0": {"coal": 5, "milk_bucket": 2, "egg": 1}, "1": {"coal": 5, "milk_bucket": 2}}}, "multiagent_cooking_cooked_beef_golden_apple_hells_kitchen": {"conversation": "We need to make cooked_beef and golden_apple together. You are supposed to make golden_apple and I am supposed to make cooked_beef, but I only have YOUR recipe and you only have access to MY recipe! Let's exchange information and get cooking!", "agent_count": 2, "target": ["cooked_beef", "golden_apple"], "type": "cooking", "timeout": 300, "recipes": {"cooked_beef": ["Step 1: Kill a cow and pick up 1 beef that is dropped.", "Step 2: Get coal from your inventory or other agents.", "Step 3: Put coal in the furnace", "Step 4: Go to furnace and use it to cook the beef."], "golden_apple": ["Step 1: Get 1 apple and 8 gold ingots from your inventory or other bots.", "Step 2: Go to the crafting table and surround the apple with the gold ingots to create a golden apple."]}, "blocked_access_to_recipe": [], "goal": {"0": "You need to make cooked_beef, but you don't have the recipe for it, your partner has it!\n\nYour partner needs to make golden_apple. You have their recipe:\nRecipe for golden_apple:\nStep 1: Get 1 apple and 8 gold ingots from your inventory or other bots.\nStep 2: Go to the crafting table and surround the apple with the gold ingots to create a golden apple.\n\nYou must communicate effectively to exchange recipe information and complete both dishes. Note: You can only guide your partner with recipe steps. You cannot help with ingredient collection or cooking.", "1": "You need to make golden_apple, but you don't have the recipe for it, your partner has it!\n\nYour partner needs to make cooked_beef. You have their recipe:\nRecipe for cooked_beef:\nStep 1: Kill a cow and pick up 1 beef that is dropped.\nStep 2: Get coal from your inventory or other agents.\nStep 3: Put coal in the furnace\nStep 4: Go to furnace and use it to cook the beef.\n\nYou must communicate effectively to exchange recipe information and complete both dishes. Note: You can only guide your partner with recipe steps. You cannot help with ingredient collection or cooking."}, "task_type": "cooking", "difficulty_metrics": {"total_recipe_steps": 6, "max_steps_per_recipe": 4, "unique_target_items": 2, "overall_difficulty_score": 5, "difficulty_category": "medium"}, "difficulty": "medium", "initial_inventory": {"0": {"gold_ingot": 5, "coal": 5}, "1": {"gold_ingot": 5, "apple": 1, "coal": 5}}}}