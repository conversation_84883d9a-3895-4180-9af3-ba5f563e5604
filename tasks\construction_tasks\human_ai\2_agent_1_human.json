{"church": {"type": "construction", "goal": "Make a structure with the blueprint below", "conversation": "Let's share materials and make a structure with the blueprint", "agent_count": 2, "human_count": 1, "timeout": 600, "blueprint": {"materials": {"oak_planks": 153, "stone_bricks": 142, "oak_door": 2, "oak_stairs": 16, "quartz_block": 1, "glass_pane": 15, "torch": 4, "oak_fence": 4}, "levels": [{"level": 0, "coordinates": [-18, -60, 29], "placement": [["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "air", "air", "air"], ["oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "air", "air", "air"], ["oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "air", "air", "air"], ["oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "air", "air", "air"], ["oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "air", "air", "air"], ["oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "air", "air", "air"], ["oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "air", "air", "air"], ["oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "air", "air", "air"], ["oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "air", "air", "air"], ["oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "air", "air", "air"], ["oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "air", "air", "air", "air"]]}, {"level": 1, "coordinates": [-18, -59, 29], "placement": [["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "stone_bricks", "stone_bricks", "oak_door", "stone_bricks", "air", "air", "air", "air", "air"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks", "air", "air", "air"], ["stone_bricks", "oak_stairs", "oak_stairs", "air", "oak_stairs", "oak_stairs", "stone_bricks", "air", "air", "air"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks", "air", "air", "air"], ["stone_bricks", "oak_stairs", "oak_stairs", "air", "oak_stairs", "oak_stairs", "stone_bricks", "air", "air", "air"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks", "air", "air", "air"], ["stone_bricks", "oak_stairs", "oak_stairs", "air", "oak_stairs", "oak_stairs", "stone_bricks", "air", "air", "air"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks", "air", "air", "air"], ["stone_bricks", "oak_stairs", "oak_stairs", "air", "oak_stairs", "oak_stairs", "stone_bricks", "air", "air", "air"], ["stone_bricks", "air", "air", "quartz_block", "air", "air", "stone_bricks", "air", "air", "air"], ["stone_bricks", "stone_bricks", "stone_bricks", "stone_bricks", "stone_bricks", "stone_bricks", "air", "air", "air", "air"]]}, {"level": 2, "coordinates": [-18, -58, 29], "placement": [["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["stone_bricks", "glass_pane", "stone_bricks", "oak_door", "stone_bricks", "glass_pane", "stone_bricks", "air", "air", "air"], ["glass_pane", "air", "air", "air", "air", "air", "glass_pane", "air", "air", "air"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks", "air", "air", "air"], ["glass_pane", "air", "air", "air", "air", "air", "glass_pane", "air", "air", "air"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks", "air", "air", "air"], ["glass_pane", "air", "air", "air", "air", "air", "glass_pane", "air", "air", "air"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks", "air", "air", "air"], ["glass_pane", "air", "air", "air", "air", "air", "glass_pane", "air", "air", "air"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks", "air", "air", "air"], ["glass_pane", "air", "air", "air", "air", "air", "glass_pane", "air", "air", "air"], ["stone_bricks", "glass_pane", "stone_bricks", "glass_pane", "stone_bricks", "glass_pane", "stone_bricks", "air", "air", "air"]]}, {"level": 3, "coordinates": [-18, -57, 29], "placement": [["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["stone_bricks", "stone_bricks", "stone_bricks", "stone_bricks", "stone_bricks", "stone_bricks", "stone_bricks", "air", "air", "air"], ["stone_bricks", "torch", "air", "air", "air", "torch", "stone_bricks", "air", "air", "air"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks", "air", "air", "air"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks", "air", "air", "air"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks", "air", "air", "air"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks", "air", "air", "air"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks", "air", "air", "air"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks", "air", "air", "air"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks", "air", "air", "air"], ["stone_bricks", "torch", "air", "air", "air", "torch", "stone_bricks", "air", "air", "air"], ["stone_bricks", "stone_bricks", "stone_bricks", "stone_bricks", "stone_bricks", "stone_bricks", "stone_bricks", "air", "air", "air"]]}, {"level": 4, "coordinates": [-18, -56, 29], "placement": [["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["stone_bricks", "stone_bricks", "stone_bricks", "stone_bricks", "stone_bricks", "stone_bricks", "stone_bricks", "air", "air", "air"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks", "air", "air", "air"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks", "air", "air", "air"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks", "air", "air", "air"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks", "air", "air", "air"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks", "air", "air", "air"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks", "air", "air", "air"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks", "air", "air", "air"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks", "air", "air", "air"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks", "air", "air", "air"], ["stone_bricks", "stone_bricks", "stone_bricks", "stone_bricks", "stone_bricks", "stone_bricks", "stone_bricks", "air", "air", "air"]]}, {"level": 5, "coordinates": [-18, -55, 29], "placement": [["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["stone_bricks", "stone_bricks", "stone_bricks", "stone_bricks", "stone_bricks", "stone_bricks", "stone_bricks", "air", "air", "air"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks", "air", "air", "air"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks", "air", "air", "air"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks", "air", "air", "air"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks", "air", "air", "air"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks", "air", "air", "air"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks", "air", "air", "air"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks", "air", "air", "air"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks", "air", "air", "air"], ["stone_bricks", "air", "air", "air", "air", "air", "stone_bricks", "air", "air", "air"], ["stone_bricks", "stone_bricks", "stone_bricks", "stone_bricks", "stone_bricks", "stone_bricks", "stone_bricks", "air", "air", "air"]]}, {"level": 6, "coordinates": [-18, -54, 29], "placement": [["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "air", "air", "air"], ["oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "air", "air", "air"], ["oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "air", "air", "air"], ["oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "air", "air", "air"], ["oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "air", "air", "air"], ["oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "air", "air", "air"], ["oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "air", "air", "air"], ["oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "air", "air", "air"], ["oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "air", "air", "air"], ["oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "air", "air", "air"], ["oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "oak_planks", "air", "air", "air"]]}, {"level": 7, "coordinates": [-18, -53, 29], "placement": [["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "stone_bricks", "air", "air", "air", "air", "air", "air"]]}, {"level": 8, "coordinates": [-18, -52, 29], "placement": [["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "stone_bricks", "air", "air", "air", "air", "air", "air"]]}, {"level": 9, "coordinates": [-18, -51, 29], "placement": [["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "stone_bricks", "air", "air", "air", "air", "air", "air"]]}, {"level": 10, "coordinates": [-18, -50, 29], "placement": [["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "oak_fence", "oak_fence", "oak_fence", "air", "air", "air", "air", "air"]]}, {"level": 11, "coordinates": [-18, -49, 29], "placement": [["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "oak_fence", "air", "air", "air", "air", "air", "air"]]}, {"level": 12, "coordinates": [-18, -48, 29], "placement": [["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"]]}, {"level": 13, "coordinates": [-18, -47, 29], "placement": [["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"]]}, {"level": 14, "coordinates": [-18, -46, 29], "placement": [["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"], ["air", "air", "air", "air", "air", "air", "air", "air", "air", "air"]]}]}, "initial_inventory": {"0": {"diamond_pickaxe": 1, "diamond_axe": 1, "diamond_shovel": 1, "oak_planks": 51, "oak_stairs": 6, "torch": 2, "oak_door": 1, "stone_bricks": 48, "quartz_block": 1, "oak_fence": 1, "glass_pane": 3}, "1": {"diamond_pickaxe": 1, "diamond_axe": 1, "diamond_shovel": 1, "oak_planks": 51, "oak_stairs": 6, "torch": 2, "oak_door": 1, "stone_bricks": 48, "quartz_block": 1, "oak_fence": 1, "glass_pane": 3}, "2": {"diamond_pickaxe": 1, "diamond_axe": 1, "diamond_shovel": 1, "oak_planks": 51, "oak_stairs": 6, "torch": 2, "oak_door": 1, "stone_bricks": 48, "quartz_block": 1, "oak_fence": 1, "glass_pane": 3}}}}